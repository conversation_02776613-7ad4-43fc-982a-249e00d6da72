# 使用官方的 Node.js 18 镜像作为基础镜像
FROM node:18

# 设置工作目录
WORKDIR /app

# 复制所有配置文件
COPY package*.json ./
COPY tsconfig.json ./
COPY tailwind.config.ts ./
COPY postcss.config.mjs ./
COPY next.config.mjs ./

# 安装所有依赖（包括 devDependencies，因为构建时需要）
RUN npm install

# 复制其余源代码
COPY . .

# 运行 Prisma generate
RUN npx prisma generate

# 定义构建参数（用于传入环境变量）
ARG NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL
ARG NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL
ARG NEXT_PUBLIC_GA_ID
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_SITE_URL

# 设置环境变量（构建时可用）
ENV NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL=$NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL
ENV NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL=$NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL
ENV NEXT_PUBLIC_GA_ID=$NEXT_PUBLIC_GA_ID
ENV NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_SITE_URL=$NEXT_PUBLIC_SITE_URL

# 构建 Next.js 应用
ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_ENV production
RUN npm run build

# 清理开发依赖（但保留构建产物）
RUN npm prune --production

# 暴露应用的端口
EXPOSE 3000

# 启动应用
CMD ["npm", "start"]
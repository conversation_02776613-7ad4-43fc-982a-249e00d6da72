import createMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';
import { NextRequest, NextResponse } from 'next/server';
import { trackApiAccess } from './utils/serverTracking';

// 创建国际化中间件
const intlMiddleware = createMiddleware(routing);

export default async function middleware(request: NextRequest) {
    // 获取请求信息
    const { pathname } = request.nextUrl;
    const userAgent = request.headers.get('user-agent') || 'Unknown';
    const ip = request.headers.get('x-forwarded-for') ||
        request.headers.get('x-real-ip') ||
        'Unknown';
    const referer = request.headers.get('referer') || 'Direct';
    const method = request.method;

    // 异步跟踪访问（不阻塞请求）
    const trackingPromise = (async () => {
        try {
            // 只跟踪页面访问，不跟踪API访问
            if (method === 'GET' && !pathname.startsWith('/_next') && !pathname.startsWith('/api')) {
                await trackApiAccess(
                    pathname,
                    method,
                    200, // 假设成功，实际状态码在响应时确定
                    request,
                    {
                        userAgent,
                        ip,
                        referer,
                        isPageView: true,
                    }
                );
            }
        } catch (error) {
            console.error('Failed to track request in middleware:', error);
        }
    })();

    // 不等待跟踪完成，静默处理错误
    trackingPromise.catch(() => {
        // 静默处理跟踪错误，不影响请求处理
    });

    // 执行国际化中间件
    const response = intlMiddleware(request);

    // 添加自定义头部（可选）
    if (response) {
        response.headers.set('X-Tracked', 'true');
        response.headers.set('X-Request-Time', new Date().toISOString());
    }

    return response;
}

export const config = {
    matcher: ['/((?!api|_next|.*\\..*).*)']
};

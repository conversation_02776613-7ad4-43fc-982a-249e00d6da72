import Stripe from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set');
}

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2024-12-18.acacia',
  typescript: true,
});

// Stripe价格配置
export const STRIPE_PRICES = {
  STANDARD_MONTHLY: process.env.STRIPE_PRICE_STANDARD_MONTHLY!,
  STANDARD_YEARLY: process.env.STRIPE_PRICE_STANDARD_YEARLY!,
  PRO_MONTHLY: process.env.STRIPE_PRICE_PRO_MONTHLY!,
  PRO_YEARLY: process.env.STRIPE_PRICE_PRO_YEARLY!,
};

// 验证所有价格ID是否配置
const requiredPrices = Object.entries(STRIPE_PRICES);
for (const [key, value] of requiredPrices) {
  if (!value) {
    throw new Error(`Stripe price ${key} is not configured`);
  }
}

export type PlanType = 'standard' | 'pro';
export type BillingInterval = 'monthly' | 'yearly';

export function getStripePriceId(plan: PlanType, interval: BillingInterval): string {
  const key = `${plan.toUpperCase()}_${interval.toUpperCase()}` as keyof typeof STRIPE_PRICES;
  return STRIPE_PRICES[key];
}

'use client'

import { useState, useEffect } from 'react';
import Image from 'next/image';

import MedGemmaMobileMenu from './MedGemmaMobileMenu';
import AuthButton from './AuthButton';
import PromoBar from './PromoBar';
import { useTranslations } from 'next-intl';
import { Link, Pathnames } from '@/i18n/routing';

export default function MedGemmaHeader() {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const [isPromoBarVisible, setIsPromoBarVisible] = useState(true);
    const t = useTranslations('pages.medgemma.content');
    const tCommon = useTranslations('components.header.nav');

    const toggleMobileMenu = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen);
    };

    const closeMobileMenu = () => {
        setIsMobileMenuOpen(false);
    };

    const handleSmoothScroll = (e: React.MouseEvent<HTMLAnchorElement>, targetId: string) => {
        e.preventDefault();
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
            const headerHeight = 80; // Approximate header height
            const targetPosition = targetElement.offsetTop - headerHeight;
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
        closeMobileMenu(); // Close mobile menu if open
    };

    return (
        <>
            {/* Promotion Bar */}
            <div className="fixed w-full top-0 z-50">
                <PromoBar onClose={() => setIsPromoBarVisible(false)} />
            </div>

            <header
                className="fixed w-full z-40 backdrop-blur-lg bg-white/70 shadow-md transition-all duration-300"
                style={{ top: isPromoBarVisible ? '40px' : '0px' }}
            >
                <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 xl:px-8">
                    <div className="flex justify-between items-center h-14 sm:h-16 md:h-18">
                        <Link href={"/" as Pathnames} className="flex items-center hover:opacity-80 transition-opacity flex-shrink-0">
                            <Image
                                src="/logo.png"
                                alt="MedGemma Logo"
                                width={24}
                                height={24}
                                className="h-5 sm:h-6 md:h-7 lg:h-8 w-auto mr-2 md:mr-3"
                            />
                            <h1 className="text-blue-800 text-base sm:text-lg md:text-xl lg:text-2xl font-bold whitespace-nowrap">MedGemma</h1>
                        </Link>

                        <div className="flex items-center space-x-1 sm:space-x-2 md:space-x-3 lg:space-x-4">
                            {/* Desktop Navigation */}
                            <nav className="hidden lg:block flex-1 max-w-2xl">
                                <ul className="flex justify-center space-x-2 xl:space-x-4 2xl:space-x-6">
                                    <li>
                                        <a
                                            href="#what-is-medgemma"
                                            onClick={(e) => handleSmoothScroll(e, 'what-is-medgemma')}
                                            className="text-blue-700 hover:text-blue-500 text-xs lg:text-sm xl:text-base 2xl:text-lg transition-colors cursor-pointer whitespace-nowrap px-1 py-1"
                                        >
                                            {t('whatIs.title')}
                                        </a>
                                    </li>
                                    <li>
                                        <a
                                            href="#features"
                                            onClick={(e) => handleSmoothScroll(e, 'features')}
                                            className="text-blue-700 hover:text-blue-500 text-xs lg:text-sm xl:text-base 2xl:text-lg transition-colors cursor-pointer whitespace-nowrap px-1 py-1"
                                        >
                                            {t('features.title')}
                                        </a>
                                    </li>
                                    <li>
                                        <a
                                            href="#how-to-use"
                                            onClick={(e) => handleSmoothScroll(e, 'how-to-use')}
                                            className="text-blue-700 hover:text-blue-500 text-xs lg:text-sm xl:text-base 2xl:text-lg transition-colors cursor-pointer whitespace-nowrap px-1 py-1"
                                        >
                                            {t('howToUse.title')}
                                        </a>
                                    </li>
                                    <li>
                                        <Link
                                            href={"/ai-medical-diagnosis" as Pathnames}
                                            className="text-blue-700 hover:text-blue-500 text-xs lg:text-sm xl:text-base 2xl:text-lg transition-colors font-medium whitespace-nowrap px-1 py-1"
                                        >
                                            {tCommon('aiDiagnosis')}
                                        </Link>
                                    </li>
                                    <li>
                                        <a
                                            href="#faq"
                                            onClick={(e) => handleSmoothScroll(e, 'faq')}
                                            className="text-blue-700 hover:text-blue-500 text-xs lg:text-sm xl:text-base 2xl:text-lg transition-colors cursor-pointer whitespace-nowrap px-1 py-1"
                                        >
                                            {t('faq.title')}
                                        </a>
                                    </li>
                                </ul>
                            </nav>



                            {/* Desktop Auth Button */}
                            <div className="hidden md:flex md:items-center">
                                <AuthButton />
                            </div>

                            {/* Mobile Menu Button */}
                            <button
                                className="lg:hidden text-blue-700 focus:outline-none focus:text-blue-500 p-1.5 hover:bg-blue-50 rounded-md transition-colors flex-shrink-0"
                                onClick={toggleMobileMenu}
                                aria-label="Toggle mobile menu"
                            >
                                <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            {/* Mobile Menu */}
            <MedGemmaMobileMenu
                isOpen={isMobileMenuOpen}
                onClose={closeMobileMenu}
            />
        </>
    );
} 
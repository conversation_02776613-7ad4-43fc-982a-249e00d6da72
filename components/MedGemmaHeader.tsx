'use client'

import { useState } from 'react';
import Image from 'next/image';
import MedGemmaLanguageSelector from './MedGemmaLanguageSelector';
import MedGemmaMobileMenu from './MedGemmaMobileMenu';
import AuthButton from './AuthButton';
import { useTranslations } from 'next-intl';
import { Link, Pathnames } from '@/i18n/routing';

export default function MedGemmaHeader() {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const t = useTranslations('pages.medgemma.content');

    const toggleMobileMenu = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen);
    };

    const closeMobileMenu = () => {
        setIsMobileMenuOpen(false);
    };

    const handleSmoothScroll = (e: React.MouseEvent<HTMLAnchorElement>, targetId: string) => {
        e.preventDefault();
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
            const headerHeight = 80; // Approximate header height
            const targetPosition = targetElement.offsetTop - headerHeight;
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
        closeMobileMenu(); // Close mobile menu if open
    };

    return (
        <>
            <header className="fixed w-full top-0 z-50 backdrop-blur-lg bg-white/70 shadow-md">
                <div className="max-w-screen-lg mx-auto px-4 py-2 md:py-3">
                    <div className="flex justify-between items-center">
                        <Link href={"/" as Pathnames} className="flex items-center hover:opacity-80 transition-opacity">
                            <Image
                                src="/logo.png"
                                alt="MedGemma Logo"
                                width={24}
                                height={24}
                                className="h-6 md:h-8 w-auto mr-2 md:mr-3"
                            />
                            <h1 className="text-blue-800 text-lg md:text-xl font-bold">MedGemma</h1>
                        </Link>

                        <div className="flex items-center space-x-2 sm:space-x-4">
                            {/* Desktop Navigation */}
                            <nav className="hidden lg:block">
                                <ul className="flex space-x-4 xl:space-x-6">
                                    <li>
                                        <a
                                            href="#what-is-medgemma"
                                            onClick={(e) => handleSmoothScroll(e, 'what-is-medgemma')}
                                            className="text-blue-700 hover:text-blue-500 text-sm xl:text-base transition-colors cursor-pointer"
                                        >
                                            {t('whatIs.title')}
                                        </a>
                                    </li>
                                    <li>
                                        <a
                                            href="#features"
                                            onClick={(e) => handleSmoothScroll(e, 'features')}
                                            className="text-blue-700 hover:text-blue-500 text-sm xl:text-base transition-colors cursor-pointer"
                                        >
                                            {t('features.title')}
                                        </a>
                                    </li>
                                    <li>
                                        <a
                                            href="#how-to-use"
                                            onClick={(e) => handleSmoothScroll(e, 'how-to-use')}
                                            className="text-blue-700 hover:text-blue-500 text-sm xl:text-base transition-colors cursor-pointer"
                                        >
                                            {t('howToUse.title')}
                                        </a>
                                    </li>
                                    <li>
                                        <a
                                            href="#faq"
                                            onClick={(e) => handleSmoothScroll(e, 'faq')}
                                            className="text-blue-700 hover:text-blue-500 text-sm xl:text-base transition-colors cursor-pointer"
                                        >
                                            {t('faq.title')}
                                        </a>
                                    </li>
                                </ul>
                            </nav>

                            {/* Desktop Auth Button */}
                            <div className="hidden md:flex md:items-center">
                                <AuthButton />
                            </div>

                            {/* Desktop Language Selector */}
                            <div className="hidden md:flex md:items-center">
                                <MedGemmaLanguageSelector />
                            </div>

                            {/* Mobile Menu Button */}
                            <button
                                className="lg:hidden text-blue-700 focus:outline-none focus:text-blue-500 p-1 hover:bg-blue-50 rounded-md transition-colors"
                                onClick={toggleMobileMenu}
                                aria-label="Toggle mobile menu"
                            >
                                <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            {/* Mobile Menu */}
            <MedGemmaMobileMenu
                isOpen={isMobileMenuOpen}
                onClose={closeMobileMenu}
            />
        </>
    );
} 
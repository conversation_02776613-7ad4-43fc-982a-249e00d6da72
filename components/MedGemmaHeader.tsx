'use client'

import { useState, useEffect } from 'react';
import Image from 'next/image';
import MedGemmaLanguageSelector from './MedGemmaLanguageSelector';
import MedGemmaMobileMenu from './MedGemmaMobileMenu';
import AuthButton from './AuthButton';
import PromoBar from './PromoBar';
import { useTranslations } from 'next-intl';
import { Link, Pathnames } from '@/i18n/routing';

export default function MedGemmaHeader() {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const [isPromoBarVisible, setIsPromoBarVisible] = useState(true);
    const t = useTranslations('pages.medgemma.content');

    const toggleMobileMenu = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen);
    };

    const closeMobileMenu = () => {
        setIsMobileMenuOpen(false);
    };

    const handleSmoothScroll = (e: React.MouseEvent<HTMLAnchorElement>, targetId: string) => {
        e.preventDefault();
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
            const headerHeight = 80; // Approximate header height
            const targetPosition = targetElement.offsetTop - headerHeight;
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
        closeMobileMenu(); // Close mobile menu if open
    };

    return (
        <>
            {/* Promotion Bar */}
            <div className="fixed w-full top-0 z-50">
                <PromoBar onClose={() => setIsPromoBarVisible(false)} />
            </div>

            <header
                className="fixed w-full z-40 backdrop-blur-lg bg-white/70 shadow-md transition-all duration-300"
                style={{ top: isPromoBarVisible ? '40px' : '0px' }}
            >
                <div className="max-w-screen-lg mx-auto px-4 py-2 md:py-3">
                    <div className="flex justify-between items-center">
                        <Link href={"/" as Pathnames} className="flex items-center hover:opacity-80 transition-opacity">
                            <Image
                                src="/logo.png"
                                alt="MedGemma Logo"
                                width={24}
                                height={24}
                                className="h-6 md:h-8 w-auto mr-2 md:mr-3"
                            />
                            <h1 className="text-blue-800 text-lg md:text-xl font-bold">MedGemma</h1>
                        </Link>

                        <div className="flex items-center space-x-2 sm:space-x-4">
                            {/* Desktop Navigation */}
                            <nav className="hidden lg:block">
                                <ul className="flex space-x-4 xl:space-x-6">
                                    <li>
                                        <a
                                            href="#what-is-medgemma"
                                            onClick={(e) => handleSmoothScroll(e, 'what-is-medgemma')}
                                            className="text-blue-700 hover:text-blue-500 text-sm xl:text-base transition-colors cursor-pointer"
                                        >
                                            {t('whatIs.title')}
                                        </a>
                                    </li>
                                    <li>
                                        <a
                                            href="#features"
                                            onClick={(e) => handleSmoothScroll(e, 'features')}
                                            className="text-blue-700 hover:text-blue-500 text-sm xl:text-base transition-colors cursor-pointer"
                                        >
                                            {t('features.title')}
                                        </a>
                                    </li>
                                    <li>
                                        <a
                                            href="#how-to-use"
                                            onClick={(e) => handleSmoothScroll(e, 'how-to-use')}
                                            className="text-blue-700 hover:text-blue-500 text-sm xl:text-base transition-colors cursor-pointer"
                                        >
                                            {t('howToUse.title')}
                                        </a>
                                    </li>
                                    <li>
                                        <a
                                            href="#faq"
                                            onClick={(e) => handleSmoothScroll(e, 'faq')}
                                            className="text-blue-700 hover:text-blue-500 text-sm xl:text-base transition-colors cursor-pointer"
                                        >
                                            {t('faq.title')}
                                        </a>
                                    </li>
                                </ul>
                            </nav>

                            {/* Desktop Upgrade Button */}
                            <div className="hidden md:flex md:items-center mr-3">
                                <Link href={"/pricing" as Pathnames}
                                    className="group relative flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-sm font-medium rounded-lg shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden"
                                >
                                    {/* Background shine effect */}
                                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>

                                    {/* Content */}
                                    <div className="relative flex items-center space-x-1.5">
                                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                                        </svg>
                                        <span>Upgrade to Pro</span>
                                    </div>
                                </Link>
                            </div>

                            {/* Desktop Auth Button */}
                            <div className="hidden md:flex md:items-center mr-3">
                                <AuthButton />
                            </div>

                            {/* Desktop Language Selector */}
                            <div className="hidden md:flex md:items-center">
                                <MedGemmaLanguageSelector />
                            </div>

                            {/* Mobile Menu Button */}
                            <button
                                className="lg:hidden text-blue-700 focus:outline-none focus:text-blue-500 p-1 hover:bg-blue-50 rounded-md transition-colors"
                                onClick={toggleMobileMenu}
                                aria-label="Toggle mobile menu"
                            >
                                <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            {/* Mobile Menu */}
            <MedGemmaMobileMenu
                isOpen={isMobileMenuOpen}
                onClose={closeMobileMenu}
            />
        </>
    );
} 
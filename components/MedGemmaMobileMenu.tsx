'use client'

import { useState, useEffect } from 'react';
import { MobileMedGemmaLanguageSelector } from './MedGemmaLanguageSelector';
import MobileAuthButton from './MobileAuthButton';
import { useTranslations } from 'next-intl';
import { Link, Pathnames } from '@/i18n/routing';

interface MedGemmaMobileMenuProps {
    isOpen: boolean;
    onClose: () => void;
}

export default function MedGemmaMobileMenu({ isOpen, onClose }: MedGemmaMobileMenuProps) {
    const t = useTranslations('pages.medgemma.content');
    const tCommon = useTranslations('components.header.nav');

    // 处理滚动锁定
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'unset';
        }

        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [isOpen]);

    const handleLinkClick = (e: React.MouseEvent<HTMLAnchorElement>, targetId: string) => {
        e.preventDefault();
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
            const headerHeight = 80; // Approximate header height
            const targetPosition = targetElement.offsetTop - headerHeight;
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
        onClose();
    };

    if (!isOpen) return null;

    return (
        <>
            {/* 背景遮罩 */}
            <div
                className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 lg:hidden"
                onClick={onClose}
            />

            {/* 菜单内容 */}
            <div className="fixed top-0 right-0 h-full w-80 max-w-[90vw] sm:max-w-[85vw] bg-white shadow-2xl z-50 lg:hidden transform transition-transform duration-300 ease-in-out">
                <div className="flex flex-col h-full">
                    {/* 头部 */}
                    <div className="flex items-center justify-between p-3 sm:p-4 border-b border-blue-200 bg-blue-50">
                        <div className="flex items-center">
                            <span className="text-base sm:text-lg font-bold text-blue-800">Menu</span>
                        </div>
                        <button
                            onClick={onClose}
                            className="p-1.5 sm:p-2 rounded-md text-blue-600 hover:bg-blue-100 transition-colors"
                            aria-label="Close menu"
                        >
                            <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    {/* 导航链接 */}
                    <div className="flex-1 overflow-y-auto p-3 sm:p-4">
                        <nav className="space-y-1 sm:space-y-2">
                            <a
                                href="#what-is-medgemma"
                                className="flex items-center py-2.5 sm:py-3 px-3 sm:px-4 text-blue-700 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors cursor-pointer"
                                onClick={(e) => handleLinkClick(e, 'what-is-medgemma')}
                            >
                                <span className="mr-2 sm:mr-3 text-lg sm:text-xl">🧠</span>
                                <span className="font-medium text-sm sm:text-base">{t('whatIs.title')}</span>
                            </a>
                            <a
                                href="#features"
                                className="flex items-center py-2.5 sm:py-3 px-3 sm:px-4 text-blue-700 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors cursor-pointer"
                                onClick={(e) => handleLinkClick(e, 'features')}
                            >
                                <span className="mr-2 sm:mr-3 text-lg sm:text-xl">⭐</span>
                                <span className="font-medium text-sm sm:text-base">{t('features.title')}</span>
                            </a>
                            <a
                                href="#how-to-use"
                                className="flex items-center py-2.5 sm:py-3 px-3 sm:px-4 text-blue-700 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors cursor-pointer"
                                onClick={(e) => handleLinkClick(e, 'how-to-use')}
                            >
                                <span className="mr-2 sm:mr-3 text-lg sm:text-xl">📖</span>
                                <span className="font-medium text-sm sm:text-base">{t('howToUse.title')}</span>
                            </a>
                            <Link
                                href={"/ai-medical-diagnosis" as Pathnames}
                                className="flex items-center py-2.5 sm:py-3 px-3 sm:px-4 text-blue-700 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                                onClick={onClose}
                            >
                                <span className="mr-2 sm:mr-3 text-lg sm:text-xl">🩺</span>
                                <span className="font-medium text-sm sm:text-base">{tCommon('aiDiagnosis')}</span>
                            </Link>
                            <Link
                                href={"/pricing" as Pathnames}
                                className="flex items-center py-2.5 sm:py-3 px-3 sm:px-4 text-blue-700 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                                onClick={onClose}
                            >
                                <span className="mr-2 sm:mr-3 text-lg sm:text-xl">💎</span>
                                <span className="font-medium text-sm sm:text-base">{tCommon('pricing')}</span>
                            </Link>
                            <a
                                href="#faq"
                                className="flex items-center py-2.5 sm:py-3 px-3 sm:px-4 text-blue-700 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors cursor-pointer"
                                onClick={(e) => handleLinkClick(e, 'faq')}
                            >
                                <span className="mr-2 sm:mr-3 text-lg sm:text-xl">❓</span>
                                <span className="font-medium text-sm sm:text-base">{t('faq.title')}</span>
                            </a>
                        </nav>

                        {/* Upgrade 按钮 */}
                        <div className="mt-4 sm:mt-6">
                            <Link href={"/pricing" as Pathnames}
                                onClick={onClose}
                                className="group relative flex items-center justify-center w-full px-4 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-lg shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden"
                            >
                                {/* Background shine effect */}
                                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>

                                {/* Content */}
                                <div className="relative flex items-center space-x-2">
                                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                                    </svg>
                                    <span>Upgrade to Pro</span>
                                </div>
                            </Link>
                        </div>

                        {/* 认证区域 */}
                        <div className="mt-4 sm:mt-6 p-3 sm:p-4 bg-blue-50 rounded-lg border border-blue-200">
                            <div className="flex items-center mb-3">
                                <span className="text-blue-600 text-sm mr-2">👤</span>
                                <span className="text-xs sm:text-sm font-medium text-blue-800">Account</span>
                            </div>
                            <MobileAuthButton />
                        </div>

                        {/* 语言切换区域 */}
                        <div className="mt-3 sm:mt-4">
                            <MobileMedGemmaLanguageSelector />
                        </div>

                        {/* 额外信息 */}
                        <div className="mt-6 sm:mt-8 p-3 sm:p-4 bg-blue-50 rounded-lg border border-blue-200">
                            <div className="flex items-center mb-2">
                                <span className="text-blue-600 text-base sm:text-lg mr-2">ℹ️</span>
                                <span className="text-xs sm:text-sm font-medium text-blue-800">About MedGemma</span>
                            </div>
                            <p className="text-xs text-blue-600 leading-relaxed">
                                Advanced medical AI models for healthcare applications.
                                Built by Google DeepMind.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
} 
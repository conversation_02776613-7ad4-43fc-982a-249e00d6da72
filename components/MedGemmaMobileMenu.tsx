'use client'

import { useState, useEffect } from 'react';
import { MobileMedGemmaLanguageSelector } from './MedGemmaLanguageSelector';
import { useTranslations } from 'next-intl';

interface MedGemmaMobileMenuProps {
    isOpen: boolean;
    onClose: () => void;
}

export default function MedGemmaMobileMenu({ isOpen, onClose }: MedGemmaMobileMenuProps) {
    const t = useTranslations('pages.medgemma.content');

    // 处理滚动锁定
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'unset';
        }

        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [isOpen]);

    const handleLinkClick = () => {
        onClose();
    };

    if (!isOpen) return null;

    return (
        <>
            {/* 背景遮罩 */}
            <div
                className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 md:hidden"
                onClick={onClose}
            />

            {/* 菜单内容 */}
            <div className="fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white shadow-2xl z-50 md:hidden transform transition-transform duration-300 ease-in-out">
                <div className="flex flex-col h-full">
                    {/* 头部 */}
                    <div className="flex items-center justify-between p-4 border-b border-blue-200 bg-blue-50">
                        <div className="flex items-center">
                            <span className="text-lg font-bold text-blue-800">Menu</span>
                        </div>
                        <button
                            onClick={onClose}
                            className="p-2 rounded-md text-blue-600 hover:bg-blue-100 transition-colors"
                            aria-label="Close menu"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    {/* 导航链接 */}
                    <div className="flex-1 overflow-y-auto p-4">
                        <nav className="space-y-2">
                            <a
                                href="#what-is-medgemma"
                                className="flex items-center py-3 px-4 text-blue-700 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                                onClick={handleLinkClick}
                            >
                                <span className="mr-3 text-xl">🧠</span>
                                <span className="font-medium">{t('whatIs.title')}</span>
                            </a>
                            <a
                                href="#features"
                                className="flex items-center py-3 px-4 text-blue-700 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                                onClick={handleLinkClick}
                            >
                                <span className="mr-3 text-xl">⭐</span>
                                <span className="font-medium">{t('features.title')}</span>
                            </a>
                            <a
                                href="#how-to-use"
                                className="flex items-center py-3 px-4 text-blue-700 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                                onClick={handleLinkClick}
                            >
                                <span className="mr-3 text-xl">📖</span>
                                <span className="font-medium">{t('howToUse.title')}</span>
                            </a>
                            <a
                                href="#faq"
                                className="flex items-center py-3 px-4 text-blue-700 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                                onClick={handleLinkClick}
                            >
                                <span className="mr-3 text-xl">❓</span>
                                <span className="font-medium">{t('faq.title')}</span>
                            </a>
                        </nav>

                        {/* 语言切换区域 */}
                        <div className="mt-6">
                            <MobileMedGemmaLanguageSelector />
                        </div>

                        {/* 额外信息 */}
                        <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
                            <div className="flex items-center mb-2">
                                <span className="text-blue-600 text-lg mr-2">ℹ️</span>
                                <span className="text-sm font-medium text-blue-800">About MedGemma</span>
                            </div>
                            <p className="text-xs text-blue-600">
                                Advanced medical AI models for healthcare applications.
                                Built by Google DeepMind.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
} 
'use client'

import { useState } from 'react';
import { Link, Pathnames } from '@/i18n/routing';
import { useTranslations } from 'next-intl';

export default function PromoBar() {
    const [isVisible, setIsVisible] = useState(true);
    const t = useTranslations('components.promoBar');

    if (!isVisible) return null;

    return (
        <div className="bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 text-white py-2 px-4 relative overflow-hidden">
            {/* Background animation */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>
            
            <div className="max-w-screen-xl mx-auto flex items-center justify-between relative z-10">
                <div className="flex items-center space-x-2 flex-1 justify-center md:justify-start">
                    {/* Icon */}
                    <svg className="w-4 h-4 text-yellow-300 animate-bounce" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
                    </svg>
                    
                    {/* Message */}
                    <span className="text-sm font-medium">
                        🎉 <span className="hidden sm:inline">Limited Time Offer: </span>
                        Get <span className="font-bold text-yellow-300">30% OFF</span> Pro Plan - 
                        <span className="hidden sm:inline"> Perfect for medical professionals!</span>
                    </span>
                    
                    {/* CTA Link */}
                    <Link 
                        href={"/pricing" as Pathnames}
                        className="ml-2 px-3 py-1 bg-white/20 hover:bg-white/30 rounded-full text-xs font-medium transition-all duration-200 hover:scale-105"
                    >
                        Claim Now →
                    </Link>
                </div>

                {/* Close button */}
                <button
                    onClick={() => setIsVisible(false)}
                    className="ml-4 p-1 hover:bg-white/20 rounded-full transition-colors duration-200 flex-shrink-0"
                    aria-label="Close promotion"
                >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    );
}

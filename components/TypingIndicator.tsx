'use client';

interface TypingIndicatorProps {
    className?: string;
}

export default function TypingIndicator({ className = '' }: TypingIndicatorProps) {
    return (
        <div className={`flex items-center space-x-1 ${className}`}>
            <div className="flex space-x-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
            <span className="text-sm text-gray-500 ml-2">AI正在思考中...</span>
        </div>
    );
}

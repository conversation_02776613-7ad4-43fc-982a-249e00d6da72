'use client'

import { useState, useRef, useEffect } from 'react';
import { useRouter, usePathname, locales, fullLanguageNames, Pathnames } from '@/i18n/routing';
import { useLocale } from 'next-intl';

type Locale = keyof typeof fullLanguageNames;

const flagEmojis: Record<string, string> = {
    'en': '🇺🇸',
    'zh': '🇨🇳',
    'tw': '🇨🇳',
    'de': '🇩🇪',
    'ja': '🇯🇵',
    'ko': '🇰🇷',
    'fr': '🇫🇷',
    'pt': '🇵🇹',
    'es': '🇪🇸',
    'vi': '🇻🇳',
    'ar': '🇸🇦',
    'nl': '🇳🇱',
    'pl': '🇵🇱',
    'ru': '🇷🇺',
};

interface LanguageSelectorProps {
    onLanguageChange?: (lang: string) => void;
}

export default function LanguageSelector({ onLanguageChange }: LanguageSelectorProps) {
    const [isOpen, setIsOpen] = useState(false);
    const currentLocale = useLocale() as Locale;
    const pathname = usePathname();
    const router = useRouter();
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleLanguageChange = async (newLocale: string) => {
        onLanguageChange?.(newLocale);
        try {
            await router.replace(pathname as any, { locale: newLocale });
            router.refresh();
            setIsOpen(false);
        } catch (error) {
            console.error('Error changing language:', error);
        }
    };

    return (
        <div className="relative" ref={dropdownRef}>
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="p-1.5 sm:p-2 rounded-lg border border-primary/20 hover:border-primary/40 transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 flex items-center justify-center bg-background/50"
                aria-label="Select language"
            >
                <span className="text-lg sm:text-xl">{flagEmojis[currentLocale]}</span>
                <span className="hidden md:inline ml-2 text-sm font-medium text-text-secondary">
                    {fullLanguageNames[currentLocale]}
                </span>
                <svg
                    className="hidden md:inline w-4 h-4 ml-1 text-text-secondary"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
            </button>
            {isOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-background border border-primary/20 rounded-lg shadow-lg py-1 z-10">
                    {locales.map((locale) => (
                        <button
                            key={locale}
                            onClick={() => handleLanguageChange(locale)}
                            className={`flex items-center space-x-2 w-full text-left px-4 py-2 text-sm hover:bg-primary/5 transition-colors
                                ${currentLocale === locale ? 'text-primary' : 'text-text-secondary'}`}
                        >
                            <span className="text-xl">{flagEmojis[locale]}</span>
                            <span className="text-sm">{fullLanguageNames[locale]}</span>
                        </button>
                    ))}
                </div>
            )}
        </div>
    );
}
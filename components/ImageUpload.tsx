'use client';

import { useRef, useState } from 'react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';

interface ImageUploadProps {
    onImageSelect: (file: File, preview: string) => void;
    onImageRemove: () => void;
    imagePreview?: string | null;
    disabled?: boolean;
}

export default function ImageUpload({ onImageSelect, onImageRemove, imagePreview, disabled = false }: ImageUploadProps) {
    const t = useTranslations('pages.medicalChat');
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [isDragOver, setIsDragOver] = useState(false);

    const handleFileSelect = (file: File) => {
        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const preview = e.target?.result as string;
                onImageSelect(file, preview);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            handleFileSelect(file);
        }
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        if (!disabled) {
            setIsDragOver(true);
        }
    };

    const handleDragLeave = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(false);
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(false);
        
        if (disabled) return;

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    };

    const handleClick = () => {
        if (!disabled) {
            fileInputRef.current?.click();
        }
    };

    if (imagePreview) {
        return (
            <div className="relative inline-block">
                <div className="relative">
                    <Image
                        src={imagePreview}
                        alt="Preview"
                        width={120}
                        height={120}
                        className="rounded-lg object-cover border-2 border-blue-200 w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-all duration-200 rounded-lg flex items-center justify-center">
                        <button
                            onClick={onImageRemove}
                            className="opacity-0 hover:opacity-100 transition-opacity duration-200 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center text-sm hover:bg-red-600 shadow-lg"
                            title="Remove image"
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
                <div className="absolute -top-2 -right-2">
                    <button
                        onClick={onImageRemove}
                        className="w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 shadow-md"
                        title="Remove image"
                    >
                        ×
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="relative">
            <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleInputChange}
                className="hidden"
                disabled={disabled}
            />
            
            <div
                onClick={handleClick}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                className={`
                    relative border-2 border-dashed rounded-lg text-center cursor-pointer transition-all duration-200 bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center
                    h-[44px] sm:h-[48px] md:h-[56px] lg:h-[64px]
                    px-3 sm:px-4 md:px-6
                    ${isDragOver
                        ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100 shadow-md'
                        : 'border-gray-300 hover:border-blue-400 hover:bg-gradient-to-br hover:from-blue-50 hover:to-blue-100 hover:shadow-sm'
                    }
                    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
                `}
            >
                <div className="flex items-center space-x-2 sm:space-x-3">
                    {/* Icon */}
                    <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center shadow-sm flex-shrink-0">
                        <svg className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>

                    {/* Text Content */}
                    <div className="flex-1 min-w-0">
                        <div className="text-xs sm:text-sm text-blue-600 font-medium truncate">
                            {t('content.chat.input.uploadHint')}
                        </div>
                        {/* Hide drag and drop text on mobile, show on desktop */}
                        <div className="hidden lg:block text-gray-500 text-xs mt-0.5">
                            or drag and drop
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

interface ChatMessageProps {
    id: string;
    type: 'user' | 'assistant';
    content: string;
    image?: string;
    timestamp: Date;
    isTyping?: boolean;
}

export default function ChatMessage({ id, type, content, image, timestamp, isTyping = false }: ChatMessageProps) {
    const [displayedContent, setDisplayedContent] = useState('');
    const [isComplete, setIsComplete] = useState(!isTyping);

    useEffect(() => {
        if (!isTyping || type === 'user') {
            setDisplayedContent(content);
            setIsComplete(true);
            return;
        }

        let currentIndex = 0;
        const typingInterval = setInterval(() => {
            if (currentIndex < content.length) {
                setDisplayedContent(content.slice(0, currentIndex + 1));
                currentIndex++;
            } else {
                setIsComplete(true);
                clearInterval(typingInterval);
            }
        }, 30); // Typing speed

        return () => clearInterval(typingInterval);
    }, [content, isTyping, type]);

    return (
        <div className={`flex ${type === 'user' ? 'justify-end' : 'justify-start'} mb-6 px-2 sm:px-0`}>
            <div className={`max-w-[85%] sm:max-w-md lg:max-w-lg xl:max-w-xl ${
                type === 'user' ? 'order-2' : 'order-1'
            }`}>
                {/* Avatar */}
                <div className={`flex items-start ${type === 'user' ? 'flex-row-reverse space-x-reverse space-x-3' : 'space-x-3'}`}>
                    <div className={`flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center shadow-sm ${
                        type === 'user'
                            ? 'bg-gradient-to-br from-blue-600 to-blue-700 text-white'
                            : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600'
                    }`}>
                        {type === 'user' ? (
                            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                            </svg>
                        ) : (
                            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        )}
                    </div>

                    {/* Message Content */}
                    <div className={`rounded-2xl px-4 py-3 sm:px-5 sm:py-4 ${
                        type === 'user'
                            ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-md'
                            : 'bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200'
                    }`}>
                        {/* Image */}
                        {image && (
                            <div className="mb-2 sm:mb-3">
                                <Image
                                    src={image}
                                    alt="Medical image"
                                    width={200}
                                    height={200}
                                    className="rounded-lg object-cover w-full h-auto max-h-32 sm:max-h-48"
                                />
                            </div>
                        )}

                        {/* Text Content */}
                        <div className={`text-xs sm:text-sm leading-relaxed ${
                            type === 'user' ? 'text-white' : 'text-gray-800'
                        }`}>
                            <div className="whitespace-pre-wrap">
                                {displayedContent}
                                {isTyping && !isComplete && (
                                    <span className="inline-block w-2 h-4 bg-current ml-1 animate-pulse" />
                                )}
                            </div>
                        </div>

                        {/* Timestamp */}
                        <div className={`text-xs mt-3 ${
                            type === 'user' ? 'text-blue-200 text-right' : 'text-gray-500 text-left'
                        }`}>
                            {timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

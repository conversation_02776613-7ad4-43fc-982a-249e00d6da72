'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

interface ChatMessageProps {
    id: string;
    type: 'user' | 'assistant';
    content: string;
    image?: string;
    timestamp: Date;
    isTyping?: boolean;
}

export default function ChatMessage({ id, type, content, image, timestamp, isTyping = false }: ChatMessageProps) {
    const [displayedContent, setDisplayedContent] = useState('');
    const [isComplete, setIsComplete] = useState(!isTyping);

    useEffect(() => {
        if (!isTyping || type === 'user') {
            setDisplayedContent(content);
            setIsComplete(true);
            return;
        }

        let currentIndex = 0;
        const typingInterval = setInterval(() => {
            if (currentIndex < content.length) {
                setDisplayedContent(content.slice(0, currentIndex + 1));
                currentIndex++;
            } else {
                setIsComplete(true);
                clearInterval(typingInterval);
            }
        }, 30); // Typing speed

        return () => clearInterval(typingInterval);
    }, [content, isTyping, type]);

    return (
        <div className={`flex ${type === 'user' ? 'justify-end' : 'justify-start'} mb-4 px-2 sm:px-0`}>
            <div className={`max-w-[85%] sm:max-w-md lg:max-w-lg xl:max-w-xl ${
                type === 'user' ? 'order-2' : 'order-1'
            }`}>
                {/* Avatar */}
                <div className={`flex items-start space-x-2 sm:space-x-3 ${type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                    <div className={`flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center ${
                        type === 'user'
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-200 text-gray-600'
                    }`}>
                        {type === 'user' ? (
                            <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                            </svg>
                        ) : (
                            <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        )}
                    </div>

                    {/* Message Content */}
                    <div className={`rounded-2xl px-3 py-2 sm:px-4 sm:py-3 ${
                        type === 'user'
                            ? 'bg-blue-600 text-white'
                            : 'bg-white border border-gray-200 shadow-sm'
                    }`}>
                        {/* Image */}
                        {image && (
                            <div className="mb-2 sm:mb-3">
                                <Image
                                    src={image}
                                    alt="Medical image"
                                    width={200}
                                    height={200}
                                    className="rounded-lg object-cover w-full h-auto max-h-32 sm:max-h-48"
                                />
                            </div>
                        )}

                        {/* Text Content */}
                        <div className={`text-xs sm:text-sm leading-relaxed ${
                            type === 'user' ? 'text-white' : 'text-gray-800'
                        }`}>
                            <div className="whitespace-pre-wrap">
                                {displayedContent}
                                {isTyping && !isComplete && (
                                    <span className="inline-block w-2 h-4 bg-current ml-1 animate-pulse" />
                                )}
                            </div>
                        </div>

                        {/* Timestamp */}
                        <div className={`text-xs mt-2 ${
                            type === 'user' ? 'text-blue-200' : 'text-gray-500'
                        }`}>
                            {timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

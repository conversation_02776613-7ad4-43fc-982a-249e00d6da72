'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import 'highlight.js/styles/github.css';
import TypingIndicator from './TypingIndicator';

interface ChatMessageProps {
    id: string;
    type: 'user' | 'assistant';
    content: string;
    image?: string;
    timestamp: Date;
    isTyping?: boolean;
}

export default function ChatMessage({ id, type, content, image, timestamp, isTyping = false }: ChatMessageProps) {
    const [displayedContent, setDisplayedContent] = useState('');
    const [isComplete, setIsComplete] = useState(!isTyping);

    useEffect(() => {
        if (!isTyping || type === 'user') {
            setDisplayedContent(content);
            setIsComplete(true);
            return;
        }

        let currentIndex = 0;
        const typingInterval = setInterval(() => {
            if (currentIndex < content.length) {
                setDisplayedContent(content.slice(0, currentIndex + 1));
                currentIndex++;
            } else {
                setIsComplete(true);
                clearInterval(typingInterval);
            }
        }, 30); // Typing speed

        return () => clearInterval(typingInterval);
    }, [content, isTyping, type]);

    return (
        <div className={`flex flex-col ${type === 'user' ? 'items-end' : 'items-start'} mb-6 px-2 sm:px-0`}>
            {/* Avatar */}
            <div className={`mb-2 ${type === 'user' ? 'self-end mr-1' : 'self-start ml-1'}`}>
                <div className={`flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center shadow-sm overflow-hidden ${
                    type === 'user'
                        ? 'bg-gradient-to-br from-blue-600 to-blue-700'
                        : 'bg-gradient-to-br from-gray-100 to-gray-200'
                }`}>
                    {type === 'user' ? (
                        <svg className="w-5 h-5 sm:w-6 sm:h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                        </svg>
                    ) : (
                        <Image
                            src="/logo.png"
                            alt="MedGemma Logo"
                            width={20}
                            height={20}
                            className="w-5 h-5 sm:w-6 sm:h-6 object-contain"
                        />
                    )}
                </div>
            </div>

            {/* Message Content */}
            <div className={`max-w-[85%] sm:max-w-md lg:max-w-lg xl:max-w-xl`}>
                <div className={`rounded-2xl px-4 py-3 sm:px-5 sm:py-4 ${
                    type === 'user'
                        ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-md'
                        : 'bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200'
                }`}>
                        {/* Image */}
                        {image && (
                            <div className="mb-2 sm:mb-3">
                                <Image
                                    src={image}
                                    alt="Medical image"
                                    width={200}
                                    height={200}
                                    className="rounded-lg object-cover w-full h-auto max-h-32 sm:max-h-48"
                                />
                            </div>
                        )}

                        {/* Text Content */}
                        <div className={`text-xs sm:text-sm leading-relaxed ${
                            type === 'user' ? 'text-white' : 'text-gray-800'
                        }`}>
                            {isTyping && type === 'assistant' && !content ? (
                                <TypingIndicator />
                            ) : type === 'assistant' ? (
                                <div className="prose prose-sm max-w-none prose-headings:text-gray-800 prose-p:text-gray-700 prose-strong:text-gray-800 prose-code:text-blue-600 prose-code:bg-blue-50 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-pre:bg-gray-100 prose-pre:border prose-blockquote:border-l-blue-500 prose-blockquote:bg-blue-50 prose-blockquote:pl-4 prose-ul:text-gray-700 prose-ol:text-gray-700 prose-li:text-gray-700">
                                    <ReactMarkdown
                                        remarkPlugins={[remarkGfm]}
                                        rehypePlugins={[rehypeHighlight]}
                                        components={{
                                            // 自定义代码块样式
                                            code: ({ className, children, ...props }: any) => {
                                                const match = /language-(\w+)/.exec(className || '');
                                                const isInline = !match;
                                                return isInline ? (
                                                    <code className="bg-blue-50 text-blue-600 px-1 py-0.5 rounded text-xs" {...props}>
                                                        {children}
                                                    </code>
                                                ) : (
                                                    <pre className="bg-gray-100 border rounded-lg p-3 overflow-x-auto">
                                                        <code className={className} {...props}>
                                                            {children}
                                                        </code>
                                                    </pre>
                                                );
                                            },
                                            // 自定义列表样式
                                            ul: ({ children }) => (
                                                <ul className="list-disc list-inside space-y-1 my-2">
                                                    {children}
                                                </ul>
                                            ),
                                            ol: ({ children }) => (
                                                <ol className="list-decimal list-inside space-y-1 my-2">
                                                    {children}
                                                </ol>
                                            ),
                                            // 自定义标题样式
                                            h1: ({ children }) => (
                                                <h1 className="text-lg font-bold text-gray-800 mt-4 mb-2 border-b border-gray-200 pb-1">
                                                    {children}
                                                </h1>
                                            ),
                                            h2: ({ children }) => (
                                                <h2 className="text-base font-bold text-gray-800 mt-3 mb-2">
                                                    {children}
                                                </h2>
                                            ),
                                            h3: ({ children }) => (
                                                <h3 className="text-sm font-semibold text-gray-800 mt-2 mb-1">
                                                    {children}
                                                </h3>
                                            ),
                                            // 自定义段落样式
                                            p: ({ children }) => (
                                                <p className="mb-2 leading-relaxed">
                                                    {children}
                                                </p>
                                            ),
                                            // 自定义引用样式
                                            blockquote: ({ children }) => (
                                                <blockquote className="border-l-4 border-blue-500 bg-blue-50 pl-4 py-2 my-2 italic">
                                                    {children}
                                                </blockquote>
                                            ),
                                            // 自定义表格样式
                                            table: ({ children }) => (
                                                <div className="overflow-x-auto my-2">
                                                    <table className="min-w-full border border-gray-200 rounded-lg">
                                                        {children}
                                                    </table>
                                                </div>
                                            ),
                                            th: ({ children }) => (
                                                <th className="border border-gray-200 bg-gray-50 px-3 py-2 text-left font-semibold text-xs">
                                                    {children}
                                                </th>
                                            ),
                                            td: ({ children }) => (
                                                <td className="border border-gray-200 px-3 py-2 text-xs">
                                                    {children}
                                                </td>
                                            ),
                                        }}
                                    >
                                        {displayedContent}
                                    </ReactMarkdown>
                                    {isTyping && !isComplete && (
                                        <span className="inline-block w-2 h-4 bg-current ml-1 animate-pulse" />
                                    )}
                                </div>
                            ) : (
                                <div className="whitespace-pre-wrap">
                                    {displayedContent}
                                    {isTyping && !isComplete && (
                                        <span className="inline-block w-2 h-4 bg-current ml-1 animate-pulse" />
                                    )}
                                </div>
                            )}
                        </div>

                    {/* Timestamp */}
                    <div className={`text-xs mt-3 ${
                        type === 'user' ? 'text-blue-200 text-right' : 'text-gray-500 text-left'
                    }`}>
                        {timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </div>
                </div>
            </div>
        </div>
    );
}

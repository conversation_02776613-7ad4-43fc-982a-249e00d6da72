'use client'

import { useState, useRef, useEffect } from 'react';
import { useRouter, usePathname } from '@/i18n/routing';
import { useLocale } from 'next-intl';

const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'zh', name: '简体中文', flag: '🇨🇳' }
];

interface MedGemmaLanguageSelectorProps {
    className?: string;
}

export default function MedGemmaLanguageSelector({ className = '' }: MedGemmaLanguageSelectorProps) {
    const [isOpen, setIsOpen] = useState(false);
    const currentLocale = useLocale();
    const router = useRouter();
    const pathname = usePathname();
    const dropdownRef = useRef<HTMLDivElement>(null);

    const currentLanguage = languages.find(lang => lang.code === currentLocale) || languages[0];

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleLanguageChange = async (langCode: string) => {
        if (langCode === currentLocale) {
            setIsOpen(false);
            return;
        }

        try {
            // 使用i18n路由系统进行语言切换
            await router.replace(pathname, { locale: langCode });
            router.refresh();
            setIsOpen(false);
        } catch (error) {
            console.error('Error changing language:', error);
            setIsOpen(false);
        }
    };

    return (
        <div className={`relative ${className}`} ref={dropdownRef}>
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="flex items-center space-x-1 px-2 py-1 md:px-3 md:py-2 rounded-md bg-blue-50 hover:bg-blue-100 border border-blue-200 hover:border-blue-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                aria-label="Select language"
            >
                <span className="text-sm md:text-base">{currentLanguage.flag}</span>
                <span className="hidden sm:inline text-xs md:text-sm font-medium text-blue-700">
                    {currentLanguage.name}
                </span>
                <svg
                    className={`w-3 h-3 md:w-4 md:h-4 text-blue-600 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''
                        }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
            </button>

            {/* 下拉菜单 */}
            {isOpen && (
                <div className="absolute right-0 mt-1 w-32 md:w-40 bg-white border border-blue-200 rounded-lg shadow-lg py-1 z-50 animate-in fade-in-0 zoom-in-95 duration-200">
                    {languages.map((language) => (
                        <button
                            key={language.code}
                            onClick={() => handleLanguageChange(language.code)}
                            className={`flex items-center space-x-2 w-full px-3 py-2 text-sm hover:bg-blue-50 transition-colors ${currentLocale === language.code
                                ? 'text-blue-700 bg-blue-50 font-medium'
                                : 'text-gray-700 hover:text-blue-700'
                                }`}
                        >
                            <span className="text-base">{language.flag}</span>
                            <span>{language.name}</span>
                            {currentLocale === language.code && (
                                <svg className="w-4 h-4 ml-auto text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                            )}
                        </button>
                    ))}
                </div>
            )}
        </div>
    );
}

// 移动端语言选择组件
export function MobileMedGemmaLanguageSelector() {
    const currentLocale = useLocale();
    const router = useRouter();
    const pathname = usePathname();

    const handleLanguageChange = async (langCode: string) => {
        if (langCode === currentLocale) return;

        try {
            // 使用i18n路由系统进行语言切换
            await router.replace(pathname, { locale: langCode });
            router.refresh();
        } catch (error) {
            console.error('Error changing language:', error);
        }
    };

    return (
        <div className="pt-2 border-t border-blue-200 mt-3">
            <div className="text-xs font-medium text-blue-600 mb-2 px-3">Language / 语言</div>
            {languages.map((language) => (
                <button
                    key={language.code}
                    onClick={() => handleLanguageChange(language.code)}
                    className={`flex items-center space-x-2 w-full py-2 px-3 rounded-md transition-colors ${currentLocale === language.code
                        ? 'text-blue-700 bg-blue-50 font-medium'
                        : 'text-blue-700 hover:bg-blue-50'
                        }`}
                >
                    <span>{language.flag}</span>
                    <span>{language.name}</span>
                    {currentLocale === language.code && (
                        <svg className="w-4 h-4 ml-auto text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                    )}
                </button>
            ))}
        </div>
    );
} 
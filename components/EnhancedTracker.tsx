'use client'

import { useEffect, useRef, useCallback } from 'react';
import { usePathname } from 'next/navigation';
import { useTracking } from '@/hooks/useTracking';

interface EnhancedTrackerProps {
    enableScrollTracking?: boolean;
    enableTimeTracking?: boolean;
    enableClickTracking?: boolean;
    scrollThresholds?: number[];
    timeTrackingInterval?: number;
}

export default function EnhancedTracker({
    enableScrollTracking = true,
    enableTimeTracking = true,
    enableClickTracking = true,
    scrollThresholds = [25, 50, 75, 90],
    timeTrackingInterval = 30000, // 30 seconds
}: EnhancedTrackerProps) {
    const pathname = usePathname();
    const { trackScrollDepth, trackTimeOnPage, trackButtonClick, trackLinkClick } = useTracking();

    const scrollTrackedRef = useRef<Set<number>>(new Set());
    const startTimeRef = useRef<number>(Date.now());
    const timeTrackingRef = useRef<NodeJS.Timeout | null>(null);

    // 重置跟踪状态（当页面变化时）
    useEffect(() => {
        scrollTrackedRef.current = new Set();
        startTimeRef.current = Date.now();

        return () => {
            if (timeTrackingRef.current) {
                clearInterval(timeTrackingRef.current);
            }
        };
    }, [pathname]);

    // 滚动深度跟踪
    const handleScroll = useCallback(() => {
        if (!enableScrollTracking) return;

        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercentage = Math.round((scrollTop / documentHeight) * 100);

        scrollThresholds.forEach(threshold => {
            if (scrollPercentage >= threshold && !scrollTrackedRef.current.has(threshold)) {
                scrollTrackedRef.current.add(threshold);
                trackScrollDepth(threshold, document.documentElement.scrollHeight);
            }
        });
    }, [enableScrollTracking, scrollThresholds, trackScrollDepth]);

    // 点击事件跟踪
    const handleClick = useCallback((event: MouseEvent) => {
        if (!enableClickTracking) return;

        const target = event.target as HTMLElement;

        // 跟踪按钮点击
        if (target.tagName === 'BUTTON' || target.closest('button')) {
            const button = target.tagName === 'BUTTON' ? target : target.closest('button');
            const buttonId = button?.id || button?.className || 'unknown-button';
            const buttonText = button?.textContent?.trim() || '';

            trackButtonClick(buttonId, buttonText, {
                tagName: button?.tagName,
                className: button?.className,
            });
        }

        // 跟踪链接点击
        if (target.tagName === 'A' || target.closest('a')) {
            const link = target.tagName === 'A' ? target as HTMLAnchorElement : target.closest('a');
            const href = link?.href || '';
            const linkText = link?.textContent?.trim() || '';
            const isExternal = href.startsWith('http') && !href.includes(window.location.hostname);

            trackLinkClick(href, linkText, isExternal);
        }
    }, [enableClickTracking, trackButtonClick, trackLinkClick]);

    // 页面停留时间跟踪
    useEffect(() => {
        if (!enableTimeTracking) return;

        const trackTimeInterval = () => {
            const timeOnPage = Math.floor((Date.now() - startTimeRef.current) / 1000);
            trackTimeOnPage(timeOnPage, pathname);
        };

        // 设置定时器
        timeTrackingRef.current = setInterval(trackTimeInterval, timeTrackingInterval);

        // 页面卸载时跟踪最终时间
        const handleBeforeUnload = () => {
            const finalTime = Math.floor((Date.now() - startTimeRef.current) / 1000);
            // 使用 navigator.sendBeacon 确保数据发送
            if (navigator.sendBeacon) {
                const data = JSON.stringify({
                    event: 'time_on_page',
                    category: 'engagement',
                    label: pathname,
                    value: finalTime,
                    page: pathname,
                    additionalData: { finalTime: true }
                });
                navigator.sendBeacon('/api/track', data);
            }
        };

        window.addEventListener('beforeunload', handleBeforeUnload);

        return () => {
            if (timeTrackingRef.current) {
                clearInterval(timeTrackingRef.current);
            }
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, [enableTimeTracking, timeTrackingInterval, pathname, trackTimeOnPage]);

    // 设置事件监听器
    useEffect(() => {
        if (enableScrollTracking) {
            window.addEventListener('scroll', handleScroll, { passive: true });
        }

        if (enableClickTracking) {
            document.addEventListener('click', handleClick, true);
        }

        return () => {
            if (enableScrollTracking) {
                window.removeEventListener('scroll', handleScroll);
            }
            if (enableClickTracking) {
                document.removeEventListener('click', handleClick, true);
            }
        };
    }, [enableScrollTracking, enableClickTracking, handleScroll, handleClick]);

    return null;
} 
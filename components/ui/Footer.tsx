import { useTranslations } from 'next-intl';
import { Link, Pathnames } from '@/i18n/routing';

export default function Footer() {
    const t = useTranslations('components.footer');

    return (
        <footer className="w-full bg-background/50 border-t border-primary/20">
            <div className="container mx-auto px-4 py-6">
                <div className="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-6">
                    <Link href={"/privacy-policy" as Pathnames}
                        className="text-sm text-gray-500 hover:text-primary transition-colors">
                        {t('links.privacyPolicy')}
                    </Link>
                    <Link href={"/terms-of-service" as Pathnames}
                        className="text-sm text-gray-500 hover:text-primary transition-colors">
                        {t('links.termsOfService')}
                    </Link>
                </div>
                <div className="mt-4 text-center">
                    <p className="text-sm text-gray-500">
                        {t('copyright')}
                    </p>
                </div>
            </div>
        </footer>
    );
}
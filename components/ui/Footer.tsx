import { useTranslations } from 'next-intl';
import { Link, Pathnames } from '@/i18n/routing';

export default function Footer() {
    const t = useTranslations('components.footer');

    return (
        <footer className="w-full bg-background/50 border-t border-primary/20">
            <div className="container mx-auto px-4 py-4 sm:py-6">
                <div className="flex flex-col sm:flex-row justify-center items-center space-y-3 sm:space-y-0 sm:space-x-6">
                    <Link href={"/privacy-policy" as Pathnames}
                        className="text-xs sm:text-sm text-gray-500 hover:text-primary transition-colors px-2 py-1 rounded hover:bg-primary/5">
                        {t('links.privacyPolicy')}
                    </Link>
                    <div className="hidden sm:block w-px h-4 bg-gray-300"></div>
                    <Link href={"/terms-of-service" as Pathnames}
                        className="text-xs sm:text-sm text-gray-500 hover:text-primary transition-colors px-2 py-1 rounded hover:bg-primary/5">
                        {t('links.termsOfService')}
                    </Link>
                </div>
                <div className="mt-3 sm:mt-4 text-center">
                    <p className="text-xs sm:text-sm text-gray-500 leading-relaxed">
                        {t('copyright')}
                    </p>
                </div>
            </div>
        </footer>
    );
}
import { useTranslations } from 'next-intl';
import { Link, Pathnames } from '@/i18n/routing';
import Image from 'next/image';

export default function Footer() {
    const t = useTranslations('pages.medgemma.content.footer');

    return (
        <footer className="bg-blue-900 text-white py-8 md:py-10">
            <div className="max-w-screen-lg mx-auto px-4">
                {/* Medical Disclaimer */}
                <div className="bg-blue-800 rounded-lg p-4 md:p-6 mb-6 md:mb-8 border-l-4 border-yellow-400">
                    <div className="flex items-start">
                        <div className="flex-shrink-0 mr-3 md:mr-4">
                            <span className="text-yellow-400 text-xl md:text-2xl">⚠️</span>
                        </div>
                        <div>
                            <h3 className="text-lg md:text-xl font-bold text-yellow-400 mb-2 md:mb-3">
                                {t('disclaimer.title')}
                            </h3>
                            <div className="text-blue-100 space-y-1 md:space-y-2 text-xs md:text-sm">
                                <p><strong>Not Medical Advice:</strong> {t('disclaimer.notMedicalAdvice')}</p>
                                <p><strong>Research Purpose Only:</strong> {t('disclaimer.researchPurpose')}</p>
                                <p><strong>Consult Healthcare Professionals:</strong> {t('disclaimer.consultProfessionals')}</p>
                                <p><strong>Use at Your Own Risk:</strong> {t('disclaimer.useAtRisk')}</p>
                                <p><strong>Validation Required:</strong> {t('disclaimer.validationRequired')}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 md:gap-8">
                    <div className="sm:col-span-2">
                        <div className="flex items-center mb-3 md:mb-4">
                            <Image src="/logo.png" alt="MedGemma Logo" width={32} height={32} className="mr-2 md:mr-3" />
                            <h3 className="text-lg md:text-xl font-bold">{t('about.title')}</h3>
                        </div>
                        <p className="text-blue-200 mb-3 md:mb-4 text-sm md:text-base">
                            {t('about.description')}
                        </p>
                        <p className="text-blue-200 text-xs md:text-sm">
                            {t('about.copyright')}
                        </p>
                    </div>
                    <div>
                        <h3 className="text-lg md:text-xl font-bold mb-3 md:mb-4">
                            {t('resources.title')}
                        </h3>
                        <ul className="space-y-2">
                            <li>
                                <a href="https://deepmind.google/models/gemma/medgemma/"
                                    target="_blank"
                                    rel="nofollow"
                                    className="text-blue-200 hover:text-white text-sm md:text-base">
                                    {t('resources.officialWebsite')}
                                </a>
                            </li>
                            <li>
                                <a href="https://developers.google.com/health-ai-developer-foundations/medgemma"
                                    target="_blank"
                                    rel="nofollow"
                                    className="text-blue-200 hover:text-white text-sm md:text-base">
                                    {t('resources.developerDocs')}
                                </a>
                            </li>
                            <li>
                                <a href="https://github.com/Google-Health/medgemma"
                                    target="_blank"
                                    rel="nofollow"
                                    className="text-blue-200 hover:text-white text-sm md:text-base">
                                    {t('resources.githubRepo')}
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h3 className="text-lg md:text-xl font-bold mb-3 md:mb-4">
                            {t('connect.title')}
                        </h3>
                        <ul className="space-y-2">
                            <li>
                                <a href="https://huggingface.co/collections/google/medgemma-release-680aade845f90bec6a3f60c4"
                                    target="_blank"
                                    rel="nofollow"
                                    className="text-blue-200 hover:text-white text-sm md:text-base">
                                    {t('connect.huggingFaceCollection')}
                                </a>
                            </li>
                            <li>
                                <a href="https://huggingface.co/spaces/google/rad_explain"
                                    target="_blank"
                                    rel="nofollow"
                                    className="text-blue-200 hover:text-white text-sm md:text-base">
                                    {t('connect.radiologyDemo')}
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h3 className="text-lg md:text-xl font-bold mb-3 md:mb-4">
                            {t('legal.title')}
                        </h3>
                        <ul className="space-y-2">
                            <li>
                                <Link href={"/privacy-policy" as Pathnames}
                                    className="text-blue-200 hover:text-white text-sm md:text-base transition-colors">
                                    {t('legal.privacyPolicy')}
                                </Link>
                            </li>
                            <li>
                                <Link href={"/terms-of-service" as Pathnames}
                                    className="text-blue-200 hover:text-white text-sm md:text-base transition-colors">
                                    {t('legal.termsOfService')}
                                </Link>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </footer>
    );
}
'use client'

import { Link, Pathnames } from '@/i18n/routing';
import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import LanguageSelector from '@/components/LanguageSelector';
import { useTranslations } from 'next-intl';

export default function Header() {
    const [isVisible, setIsVisible] = useState(false);
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const t = useTranslations('site');
    const tComponents = useTranslations('components.header');

    useEffect(() => {
        const timer = setTimeout(() => {
            setIsVisible(true);
        }, 500);

        return () => {
            clearTimeout(timer);
        };
    }, []);

    return (
        <AnimatePresence>
            {isVisible && (
                <motion.header
                    initial={{ y: -100, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{
                        duration: 0.5,
                        ease: "easeOut"
                    }}
                    className="fixed top-0 left-0 right-0 z-50 backdrop-blur-lg bg-background/80 shadow-md border-b border-primary/20"
                >
                    <div className="container mx-auto px-4 py-3 sm:py-4">
                        <nav className="flex justify-between items-center">
                            <Link href={"/" as Pathnames} className="text-xl sm:text-2xl font-bold tracking-wide text-primary">
                                {t('title')}
                            </Link>
                            <div className="flex items-center space-x-6">
                                {/* Desktop Navigation */}
                                <div className="hidden md:flex space-x-6">
                                    <Link href={"/" as Pathnames} className="text-gray-700 hover:text-primary transition-colors">
                                        {tComponents('nav.home')}
                                    </Link>
                                    <Link href={"/medgemma" as Pathnames} className="text-gray-700 hover:text-primary transition-colors">
                                        MedGemma
                                    </Link>
                                    <Link href={"/tracking-demo" as Pathnames} className="text-gray-700 hover:text-primary transition-colors">
                                        跟踪演示
                                    </Link>
                                </div>

                                {/* Mobile Menu Button */}
                                <button
                                    className="md:hidden text-gray-700 hover:text-primary focus:outline-none"
                                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                                    aria-label="Toggle mobile menu"
                                >
                                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        {isMobileMenuOpen ? (
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        ) : (
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                        )}
                                    </svg>
                                </button>

                                <LanguageSelector />
                            </div>
                        </nav>

                        {/* Mobile Navigation Menu */}
                        <AnimatePresence>
                            {isMobileMenuOpen && (
                                <motion.div
                                    initial={{ opacity: 0, height: 0 }}
                                    animate={{ opacity: 1, height: 'auto' }}
                                    exit={{ opacity: 0, height: 0 }}
                                    transition={{ duration: 0.2 }}
                                    className="md:hidden mt-4 pb-4 border-t border-primary/20"
                                >
                                    <div className="flex flex-col space-y-3 pt-4">
                                        <Link
                                            href={"/" as Pathnames}
                                            className="text-gray-700 hover:text-primary transition-colors py-2"
                                            onClick={() => setIsMobileMenuOpen(false)}
                                        >
                                            {tComponents('nav.home')}
                                        </Link>
                                        <Link
                                            href={"/medgemma" as Pathnames}
                                            className="text-gray-700 hover:text-primary transition-colors py-2"
                                            onClick={() => setIsMobileMenuOpen(false)}
                                        >
                                            MedGemma
                                        </Link>
                                        <Link
                                            href={"/tracking-demo" as Pathnames}
                                            className="text-gray-700 hover:text-primary transition-colors py-2"
                                            onClick={() => setIsMobileMenuOpen(false)}
                                        >
                                            跟踪演示
                                        </Link>
                                    </div>
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>
                </motion.header>
            )}
        </AnimatePresence>
    );
}
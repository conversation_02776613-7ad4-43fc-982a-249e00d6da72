'use client'

import { useEffect, useRef } from 'react';
import { Chart, ChartConfiguration, registerables } from 'chart.js';

Chart.register(...registerables);

interface PerformanceChartProps {
    className?: string;
}

export default function PerformanceChart({ className = '' }: PerformanceChartProps) {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const chartRef = useRef<Chart | null>(null);

    useEffect(() => {
        if (!canvasRef.current) return;

        const ctx = canvasRef.current.getContext('2d');
        if (!ctx) return;

        // Destroy existing chart if it exists
        if (chartRef.current) {
            chartRef.current.destroy();
        }

        const config: ChartConfiguration = {
            type: 'bar',
            data: {
                labels: [
                    'Medical Image Classification',
                    'Report Generation',
                    'Clinical Reasoning',
                    'Text Comprehension',
                    'Overall Performance'
                ],
                datasets: [
                    {
                        label: 'MedGemma 4B',
                        data: [82, 78, 74, 76, 80],
                        backgroundColor: 'rgba(59, 130, 246, 0.7)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'MedGemma 27B',
                        data: [72, 85, 88, 90, 85],
                        backgroundColor: 'rgba(37, 99, 235, 0.7)',
                        borderColor: 'rgba(37, 99, 235, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Other Similar Models',
                        data: [70, 72, 69, 75, 72],
                        backgroundColor: 'rgba(156, 163, 175, 0.7)',
                        borderColor: 'rgba(156, 163, 175, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: false,
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Performance Score'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Capabilities'
                        }
                    }
                }
            }
        };

        chartRef.current = new Chart(ctx, config);

        return () => {
            if (chartRef.current) {
                chartRef.current.destroy();
                chartRef.current = null;
            }
        };
    }, []);

    return (
        <div className={`relative ${className}`}>
            <canvas ref={canvasRef} />
        </div>
    );
} 
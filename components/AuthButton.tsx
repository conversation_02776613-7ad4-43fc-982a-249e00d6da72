'use client'

import { useSession, signIn, signOut } from "next-auth/react"
import { useTranslations } from 'next-intl'
import Image from 'next/image'

export default function AuthButton() {
    const { data: session, status } = useSession()
    const t = useTranslations('components.auth')

    if (status === "loading") {
        return (
            <div className="flex items-center space-x-2">
                <div className="w-6 h-6 sm:w-8 sm:h-8 bg-blue-200 rounded-full animate-pulse"></div>
                <div className="w-12 sm:w-16 h-3 sm:h-4 bg-blue-200 rounded animate-pulse"></div>
            </div>
        )
    }

    if (session) {
        return (
            <div className="flex items-center space-x-2 sm:space-x-3 w-full sm:w-auto">
                <div className="flex items-center space-x-2 flex-1 sm:flex-initial min-w-0">
                    {session.user?.image && (
                        <Image
                            src={session.user.image}
                            alt={session.user.name || 'User'}
                            width={28}
                            height={28}
                            className="rounded-full flex-shrink-0 sm:w-8 sm:h-8 border-2 border-blue-200"
                        />
                    )}
                    <span className="text-xs sm:text-sm text-blue-700 hidden sm:block truncate font-medium">
                        {session.user?.name}
                    </span>
                </div>
                <button
                    onClick={() => signOut()}
                    className="px-2 py-1 sm:px-3 sm:py-1.5 text-xs sm:text-sm bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100 transition-colors flex-shrink-0 border border-blue-200"
                >
                    {t('signOut')}
                </button>
            </div>
        )
    }

    return (
        <button
            onClick={() => signIn('google')}
            className="group relative flex items-center justify-center space-x-2 px-3 py-2 sm:px-4 bg-white border-2 border-blue-200 hover:border-blue-400 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 w-full sm:w-auto overflow-hidden"
        >
            {/* Background gradient on hover */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>

            {/* Content */}
            <div className="relative flex items-center space-x-2">
                <svg className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-blue-700 group-hover:text-blue-800 truncate transition-colors duration-200">
                    {t('signInWithGoogle')}
                </span>
            </div>
        </button>
    )
}

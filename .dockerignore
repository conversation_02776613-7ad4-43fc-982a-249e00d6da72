# Dependencies / 依赖
node_modules
.pnp
.pnp.js
.yarn

# Development / 开发相关
.git
.gitignore
.github
.vscode
.idea
.DS_Store
*.log
README.md
*.md
.editorconfig

# Testing / 测试
/coverage
__tests__
*.test.ts
*.test.tsx
*.spec.ts
*.spec.tsx

# Next.js / Next.js 相关
/.next
/out
/build

# Environment and Secrets / 环境变量和密钥
.env
.env.*
google_application_credentials.json

# Misc / 其他
*.pem
.DS_Store
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# TypeScript / TypeScript 相关
*.tsbuildinfo
next-env.d.ts

# Docker / Docker 相关
docker-compose.override.yml
docker-compose.prod.local.yml
test-prod.sh
.docker

# Database / 数据库
/data
/postgres_data
/postgres_data_prod

# Development Configs / 开发配置
.eslintrc*
.prettierrc*
.stylelintrc*
biome.json
# postcss.config.*
# tailwind.config.*

# Scripts / 脚本
deploy.sh
build.sh

# Logs / 日志
logs
*.log

# IDE / IDE 配置
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw? 
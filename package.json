{"name": "medgemma", "version": "0.1.0", "private": true, "scripts": {"dev": "NODE_ENV=development next dev", "build": "NODE_ENV=production next build", "start": "NODE_ENV=production next start", "lint": "next lint"}, "dependencies": {"@google-cloud/aiplatform": "^4.2.0", "@prisma/client": "^5.17.0", "@types/stripe": "^8.0.416", "axios": "^1.7.9", "chart.js": "^4.4.9", "clsx": "^2.1.1", "framer-motion": "^11.15.0", "googleapis": "^150.0.1", "next": "^15.1.2", "next-intl": "^3.25.0", "react": "^18", "react-dom": "^18", "stripe": "^17.5.0", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "prisma": "^5.17.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// NextAuth.js Models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  accounts      Account[]
  sessions      Session[]
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Stripe subscription fields
  subscriptionId                String?
  subscriptionStatus            String?
  currentPlan                   String   @default("free")
  subscriptionCurrentPeriodEnd  DateTime?

  // Medical AI features
  subscription  UserSubscription?
  diagnoses     MedicalDiagnosis[]
  usageStats    UsageStats?
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// User Subscription Plans
enum SubscriptionPlan {
  FREE
  STANDARD
  PRO
  CLINICAL_PLUS
}

enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  CANCELLED
  EXPIRED
}

model UserSubscription {
  id        String             @id @default(cuid())
  userId    String             @unique
  plan      SubscriptionPlan   @default(FREE)
  status    SubscriptionStatus @default(ACTIVE)
  startDate DateTime           @default(now())
  endDate   DateTime?

  // Usage limits based on plan
  dailyUploadLimit    Int @default(5)   // Free: 5, Standard: 20, Pro+: unlimited (-1)
  chatRoundsLimit     Int @default(0)   // Free: 0, Standard: 5, Pro+: unlimited (-1)
  historyDaysLimit    Int @default(0)   // Free: 0, Standard: 7, Pro+: unlimited (-1)

  // Features enabled
  hasStructuredFindings Boolean @default(false)
  hasPdfExport         Boolean @default(false)
  hasImageAnalysis     Boolean @default(false)
  hasTeachingTools     Boolean @default(false)
  hasBatchUpload       Boolean @default(false)

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Medical Diagnosis Records
enum DiagnosisStatus {
  PENDING
  COMPLETED
  FAILED
}

enum DiagnosisType {
  TEXT_ONLY
  IMAGE_ONLY
  MULTIMODAL
}

model MedicalDiagnosis {
  id          String         @id @default(cuid())
  userId      String
  title       String
  description String?
  status      DiagnosisStatus @default(PENDING)
  type        DiagnosisType

  // Input data
  textInput   String?        @db.Text
  imageUrls   String[]       // Array of image URLs

  // AI Response
  aiResponse  String?        @db.Text
  confidence  Float?         // AI confidence score
  riskLevel   String?        // LOW, MEDIUM, HIGH

  // Structured findings (Pro+ feature)
  findings    Json?          // Structured medical findings
  suggestions Json?          // Treatment suggestions

  // Metadata
  modelUsed   String?        // Which AI model was used
  processingTime Int?        // Processing time in milliseconds

  // Conversation history
  messages    DiagnosisMessage[]

  // Tags and organization
  tags        String[]
  isFavorite  Boolean        @default(false)
  isShared    Boolean        @default(false)

  user        User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  @@index([userId, createdAt])
  @@index([userId, isFavorite])
}

// Diagnosis conversation messages
enum MessageRole {
  USER
  ASSISTANT
  SYSTEM
}

model DiagnosisMessage {
  id           String      @id @default(cuid())
  diagnosisId  String
  role         MessageRole
  content      String      @db.Text
  imageUrls    String[]    // For user messages with images

  // Metadata
  modelUsed    String?     // For assistant messages
  processingTime Int?      // For assistant messages

  diagnosis    MedicalDiagnosis @relation(fields: [diagnosisId], references: [id], onDelete: Cascade)
  createdAt    DateTime    @default(now())

  @@index([diagnosisId, createdAt])
}

// User usage statistics
model UsageStats {
  id                String   @id @default(cuid())
  userId            String   @unique

  // Daily usage tracking
  dailyUploads      Int      @default(0)
  dailyUploadDate   DateTime @default(now())

  // Monthly usage tracking
  monthlyUploads    Int      @default(0)
  monthlyUploadDate DateTime @default(now())

  // Total usage
  totalDiagnoses    Int      @default(0)
  totalMessages     Int      @default(0)
  totalImages       Int      @default(0)

  // Feature usage
  pdfExports        Int      @default(0)
  teachingExports   Int      @default(0)

  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}

# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Commands

### Development
- `npm run dev` - Start development server (requires NODE_ENV=development)
- `npm run build` - Build for production (uses NODE_ENV=production)
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Database Operations
- `npx prisma migrate dev` - Run database migrations in development
- `npx prisma generate` - Generate Prisma client
- `npx prisma studio` - Open Prisma Studio for database management

### Docker Development
- `docker-compose up -d medgemma_db` - Start only the database container
- `docker-compose up -d` - Start full development environment
- `docker-compose -f docker-compose.prod.local.yml up -d` - Test production build locally

### Database Setup
After starting the database container, always run:
```bash
npx prisma migrate dev
npx prisma generate
```

## Project Architecture

### Core Structure
This is a **Next.js 15** application using the **App Router** with comprehensive internationalization support. The project follows a modular architecture with clear separation between frontend components, API routes, and utility functions.

### Key Architectural Components

**Internationalization (i18n)**:
- Built with `next-intl` for multilingual support (English/Chinese)
- Locale-based routing with `[locale]` dynamic segments
- Translation files organized in `/locales/` with nested structure
- Automatic locale detection disabled (`localeDetection: false`)

**Authentication System**:
- NextAuth.js v4 with Prisma adapter
- Google OAuth integration
- Database-backed sessions and user management

**Database & ORM**:
- PostgreSQL with Prisma ORM
- NextAuth.js standard schema (User, Account, Session, VerificationToken)
- Environment-based connection strings

**Tracking & Analytics**:
- Google Analytics integration with custom tracking
- Enhanced user behavior tracking (scroll, time, clicks)
- Server-side tracking utilities in `/utils/serverTracking.ts`
- Custom tracking hooks and components

### Directory Structure Explanation

**`app/[locale]/`** - Internationalized pages using Next.js App Router
- Each page supports localized routing
- Layout.tsx handles global providers and metadata

**`components/`** - Reusable React components
- `ui/` - Core UI components (Header, Footer)
- Authentication components (AuthButton, SessionProvider)
- Tracking components (PageViewTracker, EnhancedTracker)

**`utils/`** - Utility functions organized by purpose
- `gaLog.ts` - Google Analytics logging
- `serverTracking.ts` - Server-side tracking utilities
- `metadata.ts` - SEO and metadata helpers
- `prisma.ts` - Database connection setup

**`i18n/`** - Internationalization configuration
- `routing.ts` - Defines supported locales and pathnames
- `request.ts` - Server-side locale handling

### Environment Configuration
The project uses environment-specific configurations:
- `.env` - Local development (NODE_ENV=development)
- `.env.production` - Production deployment (NODE_ENV=production)

Key environment variables:
- `DATABASE_URL` - PostgreSQL connection string
- `NEXTAUTH_SECRET` - Authentication secret
- `OPENROUTER_API_KEY` - OpenRouter API key for AI model access
- `NEXT_PUBLIC_GA_ID` - Google Analytics tracking

### OpenRouter AI Integration
The project includes integration with OpenRouter for medical AI:
- API endpoint at `/api/openrouter-chat` for medical AI queries
- Supports multiple AI models with automatic fallback
- Includes free and premium model options
- Optimized for medical use cases with multimodal support

### Docker Deployment Strategy
Multi-environment Docker setup:
- `docker-compose.yml` - Development environment
- `docker-compose.prod.local.yml` - Local production testing  
- `docker-compose.prod.yml` - Production deployment
- Automated build and deploy scripts (`build.sh`, `deploy.sh`)

## Development Workflow

1. **Starting Development**: Use `docker-compose up -d medgemma_db` then `npm run dev`
2. **Database Changes**: Always create migrations with `npx prisma migrate dev`
3. **Internationalization**: Add translations to both `/locales/common/` and page-specific directories
4. **Testing Production Locally**: Use `docker-compose -f docker-compose.prod.local.yml up -d`
5. **Deployment**: Run `./build.sh` then `./deploy.sh` on production server

## Important Notes

- **Authentication Security**: Never commit Google Cloud credentials or OAuth secrets
- **Database Migrations**: Always test migrations locally before production deployment
- **Internationalization**: All user-facing text must be localized in both English and Chinese
- **Environment Variables**: Use appropriate `.env` file for each environment
- **OpenRouter API**: Remember that responses are for research/educational use only
import { useCallback } from 'react';
import { notifyFeishu } from '@/utils/notifyFeishu';
import { logEvent } from '@/utils/gaLog';
import { getIpInfo } from '@/utils/ipTracker';
import { TrackingEvent, TrackingCategory, ServerTrackingData } from '@/utils/trackingEvents';

export function useTracking() {
    const trackAction = useCallback(async (
        action: TrackingEvent,
        category: TrackingCategory,
        label: string,
        value: number = 1,
        additionalInfo: Record<string, any> = {}
    ) => {
        // Track in GA (non-blocking)
        Promise.resolve().then(() => {
            logEvent(action, category, label, value);
        });

        // Track in Feishu (non-blocking)
        Promise.resolve().then(async () => {
            try {
                const ipInfo = await getIpInfo();
                const timestamp = new Date().toISOString();
                const userAgent = navigator.userAgent;
                const screenSize = `${window.innerWidth}x${window.innerHeight}`;

                const message = `
🎯 User Action
Action: ${action}
Category: ${category}
Label: ${label}
Value: ${value}
Time: ${timestamp}
Path: ${window.location.pathname}
IP: ${ipInfo?.ip || 'Unknown'}
Location: ${ipInfo?.city || 'Unknown'}, ${ipInfo?.region || ''}, ${ipInfo?.country || ''}
Device: ${userAgent}
Screen: ${screenSize}
Additional Info: ${JSON.stringify(additionalInfo)}
                `.trim();

                await notifyFeishu.notify(message);
            } catch (error) {
                console.error('Failed to track action:', error);
            }
        });
    }, []);

    // 新增：通过服务端API跟踪（更可靠）
    const trackActionViaAPI = useCallback(async (
        action: TrackingEvent,
        category: TrackingCategory,
        label: string,
        value: number = 1,
        additionalInfo: Record<string, any> = {}
    ) => {
        try {
            const trackingData: ServerTrackingData = {
                event: action,
                category,
                label,
                value,
                page: window.location.pathname,
                additionalData: {
                    screenSize: `${window.innerWidth}x${window.innerHeight}`,
                    timestamp: new Date().toISOString(),
                    ...additionalInfo
                }
            };

            // 发送到服务端 API
            fetch('/api/track', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(trackingData),
            }).catch(error => {
                console.error('Failed to track via API:', error);
            });

            // 同时发送到 GA
            logEvent(action, category, label, value);
        } catch (error) {
            console.error('Failed to track action via API:', error);
        }
    }, []);

    // 新增：跟踪按钮点击
    const trackButtonClick = useCallback(async (
        buttonId: string,
        buttonText?: string,
        additionalInfo?: Record<string, any>
    ) => {
        await trackAction(
            'button_click' as TrackingEvent,
            'user_interaction' as TrackingCategory,
            buttonId,
            1,
            { buttonText, ...additionalInfo }
        );
    }, [trackAction]);

    // 新增：跟踪链接点击
    const trackLinkClick = useCallback(async (
        href: string,
        linkText?: string,
        isExternal?: boolean
    ) => {
        const event = isExternal ? 'external_link' as TrackingEvent : 'link_click' as TrackingEvent;
        await trackAction(
            event,
            'navigation' as TrackingCategory,
            href,
            1,
            { linkText, isExternal }
        );
    }, [trackAction]);

    // 新增：跟踪表单提交
    const trackFormSubmit = useCallback(async (
        formId: string,
        formType?: string,
        additionalInfo?: Record<string, any>
    ) => {
        await trackAction(
            'form_submit' as TrackingEvent,
            'conversion' as TrackingCategory,
            formId,
            1,
            { formType, ...additionalInfo }
        );
    }, [trackAction]);

    // 新增：跟踪滚动深度
    const trackScrollDepth = useCallback(async (
        percentage: number,
        pageHeight?: number
    ) => {
        await trackAction(
            'scroll_depth' as TrackingEvent,
            'engagement' as TrackingCategory,
            `${percentage}%`,
            percentage,
            { pageHeight }
        );
    }, [trackAction]);

    // 新增：跟踪页面停留时间
    const trackTimeOnPage = useCallback(async (
        timeInSeconds: number,
        pathname?: string
    ) => {
        await trackAction(
            'time_on_page' as TrackingEvent,
            'engagement' as TrackingCategory,
            pathname || window.location.pathname,
            timeInSeconds
        );
    }, [trackAction]);

    // 新增：跟踪下载
    const trackDownload = useCallback(async (
        fileName: string,
        fileType?: string,
        fileSize?: number
    ) => {
        await trackAction(
            'download' as TrackingEvent,
            'content' as TrackingCategory,
            fileName,
            fileSize || 1,
            { fileType }
        );
    }, [trackAction]);

    // 新增：跟踪搜索
    const trackSearch = useCallback(async (
        searchTerm: string,
        resultsCount?: number
    ) => {
        await trackAction(
            'search' as TrackingEvent,
            'user_interaction' as TrackingCategory,
            searchTerm,
            resultsCount || 0
        );
    }, [trackAction]);

    // 新增：跟踪错误
    const trackError = useCallback(async (
        errorType: string,
        errorMessage?: string,
        errorContext?: string
    ) => {
        await trackAction(
            'error' as TrackingEvent,
            'error' as TrackingCategory,
            errorType,
            1,
            { errorMessage, errorContext }
        );
    }, [trackAction]);

    return {
        trackAction,
        trackActionViaAPI,
        trackButtonClick,
        trackLinkClick,
        trackFormSubmit,
        trackScrollDepth,
        trackTimeOnPage,
        trackDownload,
        trackSearch,
        trackError,
    };
} 
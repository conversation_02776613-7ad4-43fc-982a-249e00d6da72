import { useState } from 'react';
import { stripePromise } from '@/lib/stripe-client';

export type PlanType = 'standard' | 'pro';
export type BillingInterval = 'monthly' | 'yearly';

interface UseStripeSubscriptionReturn {
  createCheckoutSession: (plan: PlanType, interval: BillingInterval) => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

export function useStripeSubscription(): UseStripeSubscriptionReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createCheckoutSession = async (plan: PlanType, interval: BillingInterval) => {
    setIsLoading(true);
    setError(null);

    try {
      // 创建checkout session
      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ plan, interval }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create checkout session');
      }

      const { sessionId } = await response.json();

      // 重定向到Stripe Checkout
      const stripe = await stripePromise;
      if (!stripe) {
        throw new Error('Stripe failed to load');
      }

      const { error: stripeError } = await stripe.redirectToCheckout({
        sessionId,
      });

      if (stripeError) {
        throw new Error(stripeError.message);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      console.error('Stripe checkout error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    createCheckoutSession,
    isLoading,
    error,
  };
}

#!/bin/bash

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# 项目根目录就是脚本所在目录
PROJECT_ROOT="$SCRIPT_DIR"

# 转换大小写的函数
to_lower() {
    echo "$1" | tr '[:upper:]' '[:lower:]'
}

to_upper() {
    echo "$1" | tr '[:lower:]' '[:upper:]'
}

# 显示欢迎信息
clear
echo -e "\033[1;36m=================================\033[0m"
echo -e "\033[1;36m     Next.js 项目初始化向导\033[0m"
echo -e "\033[1;36m=================================\033[0m"
echo

# 交互式收集信息
OLD_NAME="tmpl_lite"
echo -e "\033[1;33m[步骤 1/6] 项目基本信息\033[0m"
echo -e "当前模板项目: \033[1;34m$OLD_NAME\033[0m"

# 获取新项目名称
while true; do
    echo -e "\n请输入新项目名称 (只能包含小写字母、数字和下划线): "
    read -r NEW_NAME
    if [[ "$NEW_NAME" =~ ^[a-z0-9_]+$ ]]; then
        break
    else
        echo -e "\033[1;31m错误: 项目名称格式不正确\033[0m"
        echo "提示: 只能使用小写字母、数字和下划线"
    fi
done

# 获取域名配置
echo -e "\n\033[1;33m[步骤 2/6] 域名配置\033[0m"
echo -n "请输入生产环境域名 (例如: example.com): "
read -r DOMAIN
if [ -z "$DOMAIN" ]; then
    DOMAIN="domain.com"
    echo -e "使用默认域名: \033[1;33m$DOMAIN\033[0m"
fi

# 获取飞书 Webhook 配置
echo -e "\n\033[1;33m[步骤 3/6] 飞书 Webhook 配置\033[0m"
echo "请输入飞书通知 Webhook URL (直接回车使用默认值): "
read -r FEISHU_NOTIFY_WEBHOOK
if [ -z "$FEISHU_NOTIFY_WEBHOOK" ]; then
    FEISHU_NOTIFY_WEBHOOK="https://open.feishu.cn/open-apis/bot/v2/hook/51266144-06c2-47e0-860f-4f869f4b5695"
    echo -e "使用默认 Webhook"
fi

echo "请输入飞书关键词 Webhook URL (直接回车使用默认值): "
read -r FEISHU_KEY_WEBHOOK
if [ -z "$FEISHU_KEY_WEBHOOK" ]; then
    FEISHU_KEY_WEBHOOK="https://open.feishu.cn/open-apis/bot/v2/hook/51266144-06c2-47e0-860f-4f869f4b5695"
    echo -e "使用默认 Webhook"
fi

# 获取 Docker 相关信息
echo -e "\n\033[1;33m[步骤 4/6] Docker 配置\033[0m"
echo -n "请输入 Docker Hub 用户名 (默认: blowxian): "
read -r DOCKER_USER
DOCKER_USER=${DOCKER_USER:-blowxian}

# 获取数据库配置
echo -e "\n\033[1;33m[步骤 5/6] 数据库配置\033[0m"
echo -n "请输入数据库用户名前缀 (默认: ${NEW_NAME}): "
read -r DB_USER_PREFIX
DB_USER_PREFIX=${DB_USER_PREFIX:-$NEW_NAME}

echo -n "请输入数据库名称前缀 (默认: ${NEW_NAME}): "
read -r DB_NAME_PREFIX
DB_NAME_PREFIX=${DB_NAME_PREFIX:-$NEW_NAME}

# 确认信息
echo -e "\n\033[1;33m[步骤 6/6] 确认信息\033[0m"
echo -e "请确认以下信息:"
echo -e "  - 新项目名称: \033[1;32m$NEW_NAME\033[0m"
echo -e "  - 生产环境域名: \033[1;32m$DOMAIN\033[0m"
echo -e "  - Docker 用户: \033[1;32m$DOCKER_USER\033[0m"
echo -e "  - 数据库用户前缀: \033[1;32m$DB_USER_PREFIX\033[0m"
echo -e "  - 数据库名称前缀: \033[1;32m$DB_NAME_PREFIX\033[0m"
echo -e "  - 工作目录: \033[1;36m$PROJECT_ROOT\033[0m"
echo
echo -n "确认以上信息并开始初始化？[y/N] "
read -r response
if [[ ! "$response" =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
fi

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 要检查的文件列表
FILES=(
    ".env"
    ".env.production"
    "package.json"
    "package-lock.json"
    "docker-compose.yml"
    "docker-compose.prod.yml"
    "docker-compose.prod.local.yml"
    "build.sh"
    "deploy.sh"
    "test-prod.sh"
    "README.md"
)

# 初始化计数器
TOTAL_REPLACEMENTS=0

echo -e "\n\033[1;33m开始处理文件...\033[0m"

# 处理每个文件
for FILE in "${FILES[@]}"; do
    if [ -f "$FILE" ]; then
        # 替换计数器
        REPLACEMENTS=0
        
        # 定义所有替换模式
        OLD_NAME_LOWER=$(to_lower "$OLD_NAME")
        OLD_NAME_UPPER=$(to_upper "$OLD_NAME")
        NEW_NAME_LOWER=$(to_lower "$NEW_NAME")
        NEW_NAME_UPPER=$(to_upper "$NEW_NAME")
        
        # 创建替换模式数组
        PATTERNS=()
        REPLACEMENTS_ARR=()
        
        # 添加所有替换模式
        PATTERNS+=("$OLD_NAME")
        REPLACEMENTS_ARR+=("$NEW_NAME")
        
        PATTERNS+=("$OLD_NAME_LOWER")
        REPLACEMENTS_ARR+=("$NEW_NAME_LOWER")
        
        PATTERNS+=("$OLD_NAME_UPPER")
        REPLACEMENTS_ARR+=("$NEW_NAME_UPPER")
        
        PATTERNS+=("${OLD_NAME}_admin")
        REPLACEMENTS_ARR+=("${DB_USER_PREFIX}_admin")
        
        PATTERNS+=("${OLD_NAME}_db")
        REPLACEMENTS_ARR+=("${DB_NAME_PREFIX}_db")
        
        PATTERNS+=("blowxian/${OLD_NAME}")
        REPLACEMENTS_ARR+=("${DOCKER_USER}/${NEW_NAME}")
        
        # 为 .env.production 添加特殊替换模式
        if [ "$FILE" = ".env.production" ]; then
            # 域名替换
            PATTERNS+=("domain.com")
            REPLACEMENTS_ARR+=("$DOMAIN")
            
            # Webhook 替换
            PATTERNS+=("https://open.feishu.cn/open-apis/bot/v2/hook/51266144-06c2-47e0-860f-4f869f4b5695")
            REPLACEMENTS_ARR+=("$FEISHU_NOTIFY_WEBHOOK")
            
            # 第二个 Webhook 替换（如果不同的话）
            if [ "$FEISHU_KEY_WEBHOOK" != "$FEISHU_NOTIFY_WEBHOOK" ]; then
                PATTERNS+=("https://open.feishu.cn/open-apis/bot/v2/hook/51266144-06c2-47e0-860f-4f869f4b5695")
                REPLACEMENTS_ARR+=("$FEISHU_KEY_WEBHOOK")
            fi
        fi
        
        # 读取文件内容
        CONTENT=$(cat "$FILE")
        MODIFIED_CONTENT="$CONTENT"
        
        # 对每个模式进行替换并统计
        for i in "${!PATTERNS[@]}"; do
            PATTERN="${PATTERNS[$i]}"
            REPLACEMENT="${REPLACEMENTS_ARR[$i]}"
            
            # 跳过健康检查部分
            if [[ "$FILE" == "docker-compose.prod.yml" || "$FILE" == "docker-compose.prod.local.yml" ]]; then
                # 创建临时文件进行处理，保留健康检查部分
                TEMP_CONTENT=""
                IN_HEALTHCHECK=false
                
                while IFS= read -r line; do
                    if [[ "$line" == *"healthcheck:"* ]]; then
                        IN_HEALTHCHECK=true
                        TEMP_CONTENT+="$line"$'\n'
                    elif [[ "$IN_HEALTHCHECK" == true && "$line" == *"test:"* ]]; then
                        # 保留测试命令行，不替换
                        TEMP_CONTENT+="$line"$'\n'
                    elif [[ "$IN_HEALTHCHECK" == true && ("$line" == *"interval:"* || "$line" == *"timeout:"* || "$line" == *"retries:"* || "$line" == *"start_period:"*) ]]; then
                        # 保留健康检查配置，不替换
                        TEMP_CONTENT+="$line"$'\n'
                    elif [[ "$IN_HEALTHCHECK" == true && "$line" != *"healthcheck"* && "$line" != *"test:"* && "$line" != *"interval:"* && "$line" != *"timeout:"* && "$line" != *"retries:"* && "$line" != *"start_period:"* && "$line" =~ ^[[:space:]]*[a-zA-Z] ]]; then
                        # 如果遇到新的配置项，退出健康检查模式
                        IN_HEALTHCHECK=false
                        # 对当前行应用替换
                        REPLACED_LINE="${line//$PATTERN/$REPLACEMENT}"
                        if [[ "$line" != "$REPLACED_LINE" ]]; then
                            ((REPLACEMENTS++))
                        fi
                        TEMP_CONTENT+="$REPLACED_LINE"$'\n'
                    else
                        # 对非健康检查部分应用替换
                        if [[ "$IN_HEALTHCHECK" == false ]]; then
                            REPLACED_LINE="${line//$PATTERN/$REPLACEMENT}"
                            if [[ "$line" != "$REPLACED_LINE" ]]; then
                                ((REPLACEMENTS++))
                            fi
                            TEMP_CONTENT+="$REPLACED_LINE"$'\n'
                        else
                            # 在健康检查部分，保持原样
                            TEMP_CONTENT+="$line"$'\n'
                        fi
                    fi
                done <<< "$MODIFIED_CONTENT"
                
                MODIFIED_CONTENT="$TEMP_CONTENT"
            else
                # 对于其他文件，正常替换
                if echo "$MODIFIED_CONTENT" | grep -q "$PATTERN"; then
                    # 统计替换次数
                    COUNT=$(echo "$MODIFIED_CONTENT" | grep -c "$PATTERN")
                    REPLACEMENTS=$((REPLACEMENTS + COUNT))
                    
                    # 显示具体修改
                    LINE_NUMBERS=$(echo "$MODIFIED_CONTENT" | grep -n "$PATTERN" | cut -d: -f1)
                    while read -r LINE_NUM; do
                        echo -e "  \033[1;36m$FILE\033[0m:\033[1;33m$LINE_NUM\033[0m - 替换 '\033[1;31m$PATTERN\033[0m' 为 '\033[1;32m$REPLACEMENT\033[0m'"
                    done <<< "$LINE_NUMBERS"
                    
                    # 执行替换
                    MODIFIED_CONTENT=$(echo "$MODIFIED_CONTENT" | sed "s|$PATTERN|$REPLACEMENT|g")
                fi
            fi
        done
        
        # 如果有修改，写入文件
        if [ $REPLACEMENTS -gt 0 ]; then
            echo "$MODIFIED_CONTENT" > "$FILE"
            echo -e "✓ \033[1;32m$FILE\033[0m (\033[1;33m$REPLACEMENTS\033[0m 处替换)"
        else
            echo -e "- \033[1;90m$FILE\033[0m (无修改)"
        fi
        
        TOTAL_REPLACEMENTS=$((TOTAL_REPLACEMENTS + REPLACEMENTS))
    fi
done

# 显示完成信息
echo -e "\n\033[1;33m初始化完成\033[0m"
echo -e "总共完成 \033[1;32m$TOTAL_REPLACEMENTS\033[0m 处替换"

if [ $TOTAL_REPLACEMENTS -gt 0 ]; then
    echo -e "\n\033[1;32m项目已成功初始化为: $NEW_NAME\033[0m"
    echo -e "\n下一步建议:"
    echo -e "1. 检查 \033[1;36m.env\033[0m 和 \033[1;36m.env.production\033[0m 文件中的配置是否需要调整"
    echo -e "2. 更新 \033[1;36mpackage.json\033[0m 中的项目描述和版本信息"
    echo -e "3. 检查 \033[1;36m.env.production\033[0m 中的域名和 Webhook 配置"
    echo -e "4. 提交变更到版本控制系统"
else
    echo -e "\n\033[1;31m警告: 未发现需要修改的内容\033[0m"
fi 
/**
 * 国际化翻译文件索引
 * 
 * 目录结构:
 * 
 * locales/ - 根目录
 * ├── common/ - 通用翻译
 * │   ├── en.json - 英文通用翻译
 * │   └── zh.json - 中文通用翻译
 * ├── pages/ - 页面翻译
 * │   ├── home/ - 首页翻译
 * │   │   ├── en.json - 英文首页翻译
 * │   │   └── zh.json - 中文首页翻译
 * │   ├── not-found/ - 404页面翻译
 * │   │   ├── en.json - 英文404页面翻译
 * │   │   └── zh.json - 中文404页面翻译
 * │   ├── privacy/ - 隐私政策页面翻译
 * │   │   ├── en.json - 英文隐私政策页面翻译
 * │   │   └── zh.json - 中文隐私政策页面翻译
 * │   └── terms/ - 服务条款页面翻译
 * │       ├── en.json - 英文服务条款页面翻译
 * │       └── zh.json - 中文服务条款页面翻译
 */

// 当前支持的语言列表
export const supportedLocales = ['en', 'zh'] as const;

// 各语言的显示名称
export const localeNames = {
  'en': 'English',
  'zh': '简体中文',
} as const;

// 定义页面路径
export const localePaths = {
  'pages': {
    'home': 'pages/home',
    'notFound': 'pages/not-found',
    'privacy': 'pages/privacy',
    'terms': 'pages/terms',
  },
  'common': 'common',
} as const;

// 语言类型
export type Locale = typeof supportedLocales[number];

// 导出默认语言
export const defaultLocale = 'en'; 
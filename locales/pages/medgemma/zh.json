{"metadata": {"title": "MedGemma：Google DeepMind 医疗文本和图像分析的先进AI模型", "description": "了解Google DeepMind开发的MedGemma AI模型，用于医疗文本和图像处理。探索功能、用例和实施指南。", "keywords": "<PERSON><PERSON><PERSON><PERSON><PERSON>, 医疗AI, Google DeepMind, 医疗AI模型, 医疗影像AI, 临床AI解决方案"}, "content": {"hero": {"title": "MedGemma", "subtitle": "医疗文本和图像分析的先进AI模型", "description": "通过Google DeepMind的尖端MedGemma AI模型为医疗理解提供动力，推动下一代医疗应用的发展。", "modelVariants": "模型变体", "multimodalModel": "多模态模型", "textOnlyModel": "纯文本模型"}, "demo": {"title": "试用MedGemma交互式演示", "description": "体验MedGemma 4B IT模型在医疗文本和图像分析方面的强大功能"}, "whatIs": {"title": "什么是MedGemma", "description1": "MedGemma是专门设计用于理解和处理医疗文本和图像的尖端AI模型集合。由Google DeepMind开发并于2025年5月发布，MedGemma代表了医疗人工智能领域的重大进步。", "description2": "基于强大的Gemma 3架构构建，MedGemma已针对医疗应用进行了优化，为开发者提供了创建创新医疗解决方案的强大工具。", "description3": "作为健康AI开发者基金会的一部分，MedGemma旨在普及先进医疗AI技术的访问，使全球研究人员和开发者能够构建更有效的医疗应用。", "recentDevelopment": "最新发展", "launchedAt": "在Google I/O 2025发布", "releaseNote": "作为Google持续通过技术增强医疗保健努力的一部分发布"}, "features": {"title": "功能特性", "subtitle": "专为医疗应用设计的强大功能", "modelVariants": {"title": "MedGemma模型变体", "multimodal": {"title": "4B多模态模型", "description": "使用在去标识化医疗数据上预训练的SigLIP图像编码器，以40亿参数处理医疗图像和文本。"}, "textOnly": {"title": "27B纯文本模型", "description": "针对深度医疗文本理解和临床推理进行优化，拥有270亿参数。"}}, "capabilities": {"title": "核心功能", "items": ["医疗图像分类（放射学、病理学等）", "医疗图像解释和报告生成", "医疗文本理解和临床推理", "患者临床前访谈和分诊", "临床决策支持和总结"]}, "performance": {"title": "性能比较"}, "useCases": {"title": "MedGemma的用例", "healthcare": {"title": "医疗应用开发", "description": "构建基于AI的应用程序，检查医疗图像、生成报告并对患者进行分诊。"}, "research": {"title": "医学研究与创新", "description": "通过Hugging Face和Google Cloud开放访问先进AI，加速研究。"}, "clinical": {"title": "临床支持角色", "description": "增强患者访谈和临床决策支持，提高医疗效率。"}}}, "howToUse": {"title": "使用指南", "subtitle": "实施指南和适应方法", "steps": {"access": {"title": "访问MedGemma模型", "description": "MedGemma模型可在Hugging Face等平台上访问，受健康AI开发者基金会使用条款约束。"}, "adaptation": {"title": "适应方法", "promptEngineering": {"title": "提示工程", "description": "使用少样本示例并将任务分解为子任务以提高性能。"}, "fineTuning": {"title": "微调", "description": "使用您自己的医疗数据进行优化，利用GitHub笔记本等资源。"}, "agenticOrchestration": {"title": "代理编排", "description": "与网络搜索、FHIR生成器和Gemini Live等工具集成。"}}, "deployment": {"title": "部署选项", "description": "根据您的需求选择合适的部署方法：", "local": {"title": "本地部署", "description": "在本地运行模型进行实验和开发。"}, "cloud": {"title": "云部署", "description": "通过Model Garden在Vertex AI上部署为可扩展的HTTPS端点，用于生产级应用。"}}}, "considerations": {"title": "实施注意事项", "validation": {"title": "验证要求", "description": "MedGemma模型开箱即用并非临床级别。开发者必须在生产环境中部署之前验证性能并进行必要的改进。"}, "terms": {"title": "使用条款", "description": "MedGemma的使用受健康AI开发者基金会使用条款约束，开发者在访问模型之前必须审查并同意这些条款。"}}}, "faq": {"title": "常见问题", "subtitle": "关于MedGemma的常见问题", "items": [{"question": "4B多模态和27B纯文本MedGemma模型之间的主要区别是什么？", "answer": "4B多模态模型使用SigLIP图像编码器以40亿参数处理医疗图像和文本。27B纯文本模型专注于文本处理，拥有270亿参数，针对更深层的医疗文本理解和临床推理进行了优化。"}, {"question": "MedGemma模型是否可以开箱即用于临床使用？", "answer": "不，MedGemma模型开箱即用并不被认为是临床级别的。开发者必须在生产环境中部署之前验证其性能并进行必要的改进，特别是涉及患者护理的应用。"}, {"question": "我如何访问MedGemma模型进行开发工作？", "answer": "MedGemma模型可在Hugging Face和Google Cloud等平台上访问，受健康AI开发者基金会使用条款约束。您可以在本地运行它们进行实验，或通过Google Cloud部署用于生产级应用。"}, {"question": "4B多模态模型可以处理哪些类型的医疗图像？", "answer": "4B多模态模型在多样化的医疗图像上进行预训练，包括胸部X光、皮肤病学图像、眼科图像和组织病理学切片，使其适用于各种医疗影像任务。"}, {"question": "哪些适应方法可以提高MedGemma在特定任务上的性能？", "answer": "开发者可以使用提示工程（少样本示例）、使用自己的医疗数据进行微调，以及与网络搜索、FHIR生成器和Gemini Live等工具的代理编排来增强特定用例的性能。"}, {"question": "MedGemma是何时由谁发布的？", "answer": "MedGemma于2025年5月20-22日左右在Google I/O 2025期间由Google DeepMind正式发布，作为他们通过技术增强医疗保健持续努力的一部分。"}, {"question": "MedGemma与同等规模的类似模型相比如何？", "answer": "根据Google开发者上的模型卡，MedGemma的基线性能与同等规模的模型相比表现强劲。它已在临床相关基准上进行评估，包括开放数据集和策划数据集，重点关注任务的专家人工评估。"}, {"question": "是否有可用于微调MedGemma的资源？", "answer": "是的，包括GitHub上的笔记本在内的资源可用于促进微调，例如Google的MedGemma GitHub存储库中提供的使用LoRA的微调示例。"}, {"question": "运行MedGemma模型的硬件要求是什么？", "answer": "硬件要求取决于模型变体。根据Google AI的帖子，MedGemma模型设计为高效，能够在单个GPU上运行微调和推理，使其比一些更大的模型更易于访问。"}, {"question": "MedGemma是否支持多语言医学术语？", "answer": "基于社区讨论，对MedGemma在非英语医学术语（如日语医学术语）方面的性能存在疑问。这表明多语言支持可能有所不同，可能是未来改进或微调的一个领域。"}]}, "footer": {"disclaimer": {"title": "医疗免责声明", "notMedicalAdvice": "非医疗建议：本网站提供的关于MedGemma的信息仅用于教育和信息目的。它不作为医疗建议、诊断或治疗。", "researchPurpose": "仅用于研究目的：MedGemma模型专为研究和开发目的而设计。它们不是临床级工具，在没有适当验证和监管批准的情况下不应用于实际患者护理。", "consultProfessionals": "咨询医疗专业人员：在做出医疗决定时，请始终咨询合格的医疗专业人员。不要仅依赖AI模型得出健康相关结论。", "useAtRisk": "使用风险自负：用户对MedGemma模型的任何应用承担全部责任。开发者和本网站对基于AI模型输出做出的医疗决定不承担任何责任。", "validationRequired": "需要验证：任何临床应用都需要在医疗环境中部署之前进行彻底验证、监管合规和专家医疗监督。"}, "about": {"title": "关于MedGemma", "description": "MedGemma代表了医疗AI的重大进步，为医疗保健中的图像和文本处理提供了强大的功能。", "copyright": "© 2025 Google DeepMind。关于MedGemma的所有信息均基于公开可用数据。"}, "resources": {"title": "资源", "officialWebsite": "MedGemma官方网站", "developerDocs": "开发者文档", "githubRepo": "GitHub存储库"}, "connect": {"title": "连接", "huggingFaceCollection": "Hugging Face集合", "radiologyDemo": "放射学解释器演示"}, "legal": {"title": "法律", "privacyPolicy": "隐私政策", "termsOfService": "服务条款"}}}}
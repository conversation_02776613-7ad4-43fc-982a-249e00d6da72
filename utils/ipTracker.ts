import axios from 'axios';

interface IpInfo {
    ip: string;
    city?: string;
    region?: string;
    country?: string;
    timezone?: string;
}

interface CachedIpInfo extends IpInfo {
    timestamp: number;
}

const CACHE_KEY = 'ip_info_cache';
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

export async function getIpInfo(): Promise<IpInfo | null> {
    // 确保只在客户端执行
    if (typeof window === 'undefined') {
        return null;
    }

    try {
        // 尝试从缓存获取
        const cachedData = localStorage.getItem(CACHE_KEY);
        if (cachedData) {
            const parsed: CachedIpInfo = JSON.parse(cachedData);
            const now = Date.now();

            // 检查缓存是否有效
            if (now - parsed.timestamp < CACHE_DURATION) {
                const { timestamp, ...ipInfo } = parsed;
                return ipInfo;
            }
            // 如果缓存过期，删除它
            localStorage.removeItem(CACHE_KEY);
        }

        // 获取新数据
        const ipResponse = await axios.get('https://api.ipify.org?format=json');
        const ip = ipResponse.data.ip;

        const locationResponse = await axios.get(`https://ipapi.co/${ip}/json/`);

        const ipInfo: IpInfo = {
            ip,
            city: locationResponse.data.city,
            region: locationResponse.data.region,
            country: locationResponse.data.country_name,
            timezone: locationResponse.data.timezone
        };

        // 缓存数据
        const cacheData: CachedIpInfo = {
            ...ipInfo,
            timestamp: Date.now()
        };
        localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));

        return ipInfo;
    } catch (error) {
        console.error('Failed to fetch IP info:', error);
        return null;
    }
} 
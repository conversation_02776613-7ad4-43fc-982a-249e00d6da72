import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export const getStripeCustomerID = async ({
    email,
    user_id
}: {
    email: string;
    user_id: string;
}) => {
    try {
        // Search for existing customer
        const existingCustomer = await stripe.customers.list({
            email: email,
            limit: 1,
        });

        if (existingCustomer.data.length > 0) {
            return existingCustomer.data[0].id;
        }

        // Create new customer if not exists
        const customer = await stripe.customers.create({
            email: email,
            metadata: {
                user_id: user_id,
            },
        });

        return customer.id;
    } catch (error) {
        console.error('Error in getStripeCustomerID:', error);
        throw error;
    }
};
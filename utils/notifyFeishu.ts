import axios from 'axios';

const FEISHU_WEBHOOK_URL = process.env.NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL;
const FEISHU_KEY_WEBHOOK_URL = process.env.NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL;

// 临时备用 Webhook URL（仅用于确保线上环境正常工作）
// 生产环境应该使用环境变量配置
const FALLBACK_WEBHOOK_URL = 'https://open.feishu.cn/open-apis/bot/v2/hook/51266144-06c2-47e0-860f-4f869f4b5695';

async function sendFeishuNotification(webhookUrl: string | undefined, message: string) {
    // 使用环境变量或备用URL
    const finalWebhookUrl = webhookUrl || FALLBACK_WEBHOOK_URL;

    // 如果仍然没有 webhook URL，静默返回
    if (!finalWebhookUrl) {
        // 在开发环境下显示调试信息
        if (process.env.NODE_ENV === 'development') {
            console.warn('飞书 Webhook URL 未配置，跳过通知发送');
        }
        return;
    }

    try {
        await axios.post(finalWebhookUrl, {
            msg_type: 'text',
            content: { text: message },
        });

        // 在开发环境下显示成功信息
        if (process.env.NODE_ENV === 'development') {
            console.log('飞书通知发送成功');
        }
    } catch (error) {
        // 在开发环境下才显示错误信息
        if (process.env.NODE_ENV === 'development') {
            console.error('飞书通知发送失败:', error);
        }
    }
}

export const notifyFeishu = {
    notify: (message: string) => sendFeishuNotification(FEISHU_WEBHOOK_URL, message),
    keyNotify: (message: string) => sendFeishuNotification(FEISHU_KEY_WEBHOOK_URL, message),
};

// 导出调试信息
export const debugInfo = {
    hasWebhookUrl: !!FEISHU_WEBHOOK_URL,
    hasKeyWebhookUrl: !!FEISHU_KEY_WEBHOOK_URL,
    nodeEnv: process.env.NODE_ENV,
    webhookUrl: process.env.NODE_ENV === 'development' ? FEISHU_WEBHOOK_URL : '[HIDDEN]',
    usingFallback: !FEISHU_WEBHOOK_URL
};
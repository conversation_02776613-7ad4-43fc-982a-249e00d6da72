import { notifyFeishu } from './notifyFeishu';
import { TrackingEvent, TrackingCategory, ServerTrackingData } from './trackingEvents';

interface ServerTrackingContext {
    ip?: string;
    userAgent?: string;
    referer?: string;
    timestamp?: string;
    headers?: Record<string, string>;
}

/**
 * 服务端用户行为跟踪
 * 直接发送飞书通知，适用于 API 路由、中间件等服务端场景
 */
export class ServerTracker {
    private context: ServerTrackingContext;

    constructor(context: ServerTrackingContext = {}) {
        this.context = {
            timestamp: new Date().toISOString(),
            ...context
        };
    }

    /**
     * 跟踪用户行为事件
     */
    async trackEvent(data: ServerTrackingData): Promise<void> {
        try {
            let message = `
🎯 Server-Side Tracking
Event: ${data.event}
Category: ${data.category}
Label: ${data.label}
Value: ${data.value || 0}
Time: ${this.context.timestamp}
            `.trim();

            // 添加页面信息
            if (data.page) {
                message += `\nPage: ${data.page}`;
            }

            // 添加用户信息
            if (data.userId) {
                message += `\nUser ID: ${data.userId}`;
            }

            if (data.sessionId) {
                message += `\nSession ID: ${data.sessionId}`;
            }

            // 添加请求上下文信息
            if (this.context.ip) {
                message += `\nIP: ${this.context.ip}`;
            }

            if (this.context.userAgent) {
                message += `\nUser-Agent: ${this.context.userAgent}`;
            }

            if (this.context.referer) {
                message += `\nReferer: ${this.context.referer}`;
            }

            // 添加额外数据
            if (data.additionalData && Object.keys(data.additionalData).length > 0) {
                message += `\nAdditional Data: ${JSON.stringify(data.additionalData)}`;
            }

            await notifyFeishu.notify(message);
        } catch (error) {
            console.error('Failed to track server event:', error);
        }
    }

    /**
     * 跟踪 API 访问
     */
    async trackApiAccess(
        endpoint: string,
        method: string,
        statusCode: number,
        additionalData?: Record<string, any>
    ): Promise<void> {
        await this.trackEvent({
            event: 'api_access' as TrackingEvent,
            category: 'api' as TrackingCategory,
            label: `${method} ${endpoint}`,
            value: statusCode,
            additionalData: {
                endpoint,
                method,
                statusCode,
                ...additionalData
            }
        });
    }

    /**
     * 跟踪错误
     */
    async trackError(
        error: Error | string,
        context?: string,
        additionalData?: Record<string, any>
    ): Promise<void> {
        const errorMessage = error instanceof Error ? error.message : error;
        const errorStack = error instanceof Error ? error.stack : undefined;

        await this.trackEvent({
            event: 'error' as TrackingEvent,
            category: 'error' as TrackingCategory,
            label: context || 'unknown_error',
            additionalData: {
                error: errorMessage,
                stack: errorStack,
                context,
                ...additionalData
            }
        });
    }

    /**
     * 跟踪页面访问（服务端渲染）
     */
    async trackPageView(
        pathname: string,
        locale?: string,
        additionalData?: Record<string, any>
    ): Promise<void> {
        await this.trackEvent({
            event: 'page_view' as TrackingEvent,
            category: 'navigation' as TrackingCategory,
            label: pathname,
            page: pathname,
            additionalData: {
                locale,
                ssr: true,
                ...additionalData
            }
        });
    }
}

/**
 * 从 Next.js 请求对象创建服务端跟踪器
 */
export function createServerTracker(request?: Request): ServerTracker {
    if (!request) {
        return new ServerTracker();
    }

    const headers = request.headers;
    const ip = headers.get('x-forwarded-for') ||
        headers.get('x-real-ip') ||
        'Unknown';
    const userAgent = headers.get('user-agent') || 'Unknown';
    const referer = headers.get('referer') || 'Direct';

    return new ServerTracker({
        ip,
        userAgent,
        referer,
        headers: Object.fromEntries(headers.entries())
    });
}

/**
 * 便捷函数：快速跟踪事件
 */
export async function trackServerEvent(
    event: TrackingEvent,
    category: TrackingCategory,
    label: string,
    value?: number,
    context?: ServerTrackingContext,
    additionalData?: Record<string, any>
): Promise<void> {
    const tracker = new ServerTracker(context);
    await tracker.trackEvent({
        event,
        category,
        label,
        value,
        additionalData
    });
}

/**
 * 便捷函数：快速跟踪 API 访问
 */
export async function trackApiAccess(
    endpoint: string,
    method: string,
    statusCode: number,
    request?: Request,
    additionalData?: Record<string, any>
): Promise<void> {
    const tracker = createServerTracker(request);
    await tracker.trackApiAccess(endpoint, method, statusCode, additionalData);
} 
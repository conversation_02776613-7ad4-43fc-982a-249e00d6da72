export enum TrackingEvents {
    PAGE_VIEW = 'page_view',
    NAVIGATION_CLICK = 'navigation_click',
    LANGUAGE_CHANGE = 'language_change',
    MOBILE_MENU_TOGGLE = 'mobile_menu_toggle',
    DROPDOWN_OPEN = 'dropdown_open',
    BUTTON_CLICK = 'button_click',
    LINK_CLICK = 'link_click',
    FORM_SUBMIT = 'form_submit',
    SCROLL_DEPTH = 'scroll_depth',
    TIME_ON_PAGE = 'time_on_page',
    DOWNLOAD = 'download',
    EXTERNAL_LINK = 'external_link',
    SEARCH = 'search',
    LOGIN = 'login',
    LOGOUT = 'logout',
    SIGNUP = 'signup',
    ERROR = 'error',
    API_CALL = 'api_call',
    MEDGEMMA_DEMO_USE = 'medgemma_demo_use',
    PERFORMANCE_CHART_VIEW = 'performance_chart_view',
    FAQ_EXPAND = 'faq_expand',
    CONTACT_FORM = 'contact_form',
}

export enum TrackingCategories {
    NAVIGATION = 'navigation',
    USER_INTERACTION = 'user_interaction',
    LANGUAGE = 'language',
    SKILLS = 'skills',
    ENGAGEMENT = 'engagement',
    CONVERSION = 'conversion',
    PERFORMANCE = 'performance',
    ERROR = 'error',
    BUSINESS = 'business',
    SOCIAL = 'social',
    CONTENT = 'content',
    AUTHENTICATION = 'authentication',
    API = 'api',
}

export type TrackingEvent = typeof TrackingEvents[keyof typeof TrackingEvents];
export type TrackingCategory = typeof TrackingCategories[keyof typeof TrackingCategories];

export interface ServerTrackingData {
    event: TrackingEvent;
    category: TrackingCategory;
    label: string;
    value?: number;
    page?: string;
    userId?: string;
    sessionId?: string;
    additionalData?: Record<string, any>;
}

export const CommonTrackingEvents = {
    PAGE_VIEWS: {
        HOME: { event: TrackingEvents.PAGE_VIEW, category: TrackingCategories.NAVIGATION, label: 'home' },
        MEDGEMMA: { event: TrackingEvents.PAGE_VIEW, category: TrackingCategories.NAVIGATION, label: 'medgemma' },
        PRIVACY: { event: TrackingEvents.PAGE_VIEW, category: TrackingCategories.NAVIGATION, label: 'privacy' },
        TERMS: { event: TrackingEvents.PAGE_VIEW, category: TrackingCategories.NAVIGATION, label: 'terms' },
    },
    USER_ACTIONS: {
        LANGUAGE_SWITCH_EN: { event: TrackingEvents.LANGUAGE_CHANGE, category: TrackingCategories.LANGUAGE, label: 'english' },
        LANGUAGE_SWITCH_ZH: { event: TrackingEvents.LANGUAGE_CHANGE, category: TrackingCategories.LANGUAGE, label: 'chinese' },
        MOBILE_MENU_OPEN: { event: TrackingEvents.MOBILE_MENU_TOGGLE, category: TrackingCategories.NAVIGATION, label: 'open' },
        MOBILE_MENU_CLOSE: { event: TrackingEvents.MOBILE_MENU_TOGGLE, category: TrackingCategories.NAVIGATION, label: 'close' },
    },
    BUSINESS_ACTIONS: {
        MEDGEMMA_DEMO_START: { event: TrackingEvents.MEDGEMMA_DEMO_USE, category: TrackingCategories.BUSINESS, label: 'demo_start' },
        MEDGEMMA_DEMO_SUBMIT: { event: TrackingEvents.MEDGEMMA_DEMO_USE, category: TrackingCategories.BUSINESS, label: 'demo_submit' },
        PERFORMANCE_CHART_HOVER: { event: TrackingEvents.PERFORMANCE_CHART_VIEW, category: TrackingCategories.ENGAGEMENT, label: 'chart_hover' },
        FAQ_OPEN: { event: TrackingEvents.FAQ_EXPAND, category: TrackingCategories.ENGAGEMENT, label: 'faq_open' },
    }
}; 
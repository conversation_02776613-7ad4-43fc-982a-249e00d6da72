<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Learn all about MedGemma AI models by Google DeepMind for medical text and image processing. Discover features, use cases, and implementation guides.">
    <meta name="keywords" content="MedGemma, medical AI, Google DeepMind, healthcare AI models, medical imaging AI, clinical AI solutions">
    <title>MedGemma: Advanced AI Models for Medical Text and Image Analysis | Google DeepMind</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <script defer data-domain="medgemma.org" src="https://app.pageview.app/js/script.js"></script>

    <!-- TailwindCSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'tech-blue': {
                            100: '#E6F0FF',
                            200: '#CCE0FF',
                            300: '#99C0FF',
                            400: '#66A0FF',
                            500: '#3380FF',
                            600: '#0055CC',
                            700: '#004099',
                            800: '#002B66',
                            900: '#001533',
                        },
                    },
                }
            }
        }
    </script>
    
    <style>
        .glass-effect {
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.7);
        }
        
        .gradient-tech {
            background: linear-gradient(135deg, rgba(51, 128, 255, 0.9) 0%, rgba(51, 128, 255, 0.4) 100%);
        }
        
        .gradient-tech-alt {
            background: linear-gradient(135deg, rgba(0, 85, 204, 0.9) 0%, rgba(0, 85, 204, 0.4) 100%);
        }
    </style>
</head>
<body class="bg-white">
    <!-- Sticky Header with Glass Effect -->
    <header class="glass-effect fixed w-full top-0 z-50 shadow-md">
        <div class="max-w-screen-lg mx-auto px-4 py-2 md:py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <img src="logo.png" alt="MedGemma Logo" class="h-6 md:h-8 w-auto mr-2 md:mr-3">
                    <h1 class="text-tech-blue-800 text-lg md:text-xl font-bold">MedGemma</h1>
                </div>
                
                <!-- Desktop Navigation -->
                <nav class="hidden md:block">
                    <ul class="flex space-x-6">
                        <li><a href="#what-is-medgemma" class="text-tech-blue-700 hover:text-tech-blue-500 text-sm lg:text-base">What is MedGemma</a></li>
                        <li><a href="#features" class="text-tech-blue-700 hover:text-tech-blue-500 text-sm lg:text-base">Features</a></li>
                        <li><a href="#how-to-use" class="text-tech-blue-700 hover:text-tech-blue-500 text-sm lg:text-base">How to Use</a></li>
                        <li><a href="#faq" class="text-tech-blue-700 hover:text-tech-blue-500 text-sm lg:text-base">FAQ</a></li>
                    </ul>
                </nav>
                
                <!-- Mobile Menu Button -->
                <button class="md:hidden text-tech-blue-700 focus:outline-none focus:text-tech-blue-500" id="mobile-menu-btn">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
            
            <!-- Mobile Navigation Menu -->
            <div class="md:hidden hidden mt-4 pb-2" id="mobile-menu">
                <nav>
                    <ul class="space-y-2">
                        <li><a href="#what-is-medgemma" class="block text-tech-blue-700 hover:text-tech-blue-500 py-2 px-2 rounded hover:bg-tech-blue-50 mobile-menu-link">What is MedGemma</a></li>
                        <li><a href="#features" class="block text-tech-blue-700 hover:text-tech-blue-500 py-2 px-2 rounded hover:bg-tech-blue-50 mobile-menu-link">Features</a></li>
                        <li><a href="#how-to-use" class="block text-tech-blue-700 hover:text-tech-blue-500 py-2 px-2 rounded hover:bg-tech-blue-50 mobile-menu-link">How to Use</a></li>
                        <li><a href="#faq" class="block text-tech-blue-700 hover:text-tech-blue-500 py-2 px-2 rounded hover:bg-tech-blue-50 mobile-menu-link">FAQ</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>
    
    <!-- Hero Section -->
    <section class="pt-24 md:pt-28 pb-12 md:pb-16 bg-gradient-to-b from-tech-blue-100 to-white relative overflow-hidden">
        <!-- Background Image -->
        <div class="absolute inset-0 z-0">
            <img src="bg.png" alt="Medical AI Background" class="w-full h-full object-cover opacity-10">
        </div>
        
        <!-- Content -->
        <div class="max-w-screen-lg mx-auto px-4 relative z-10">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-8 md:mb-0 text-center md:text-left">
                    <h1 class="text-4xl sm:text-5xl md:text-6xl font-bold text-tech-blue-800 mb-4">Med<span class="text-tech-blue-600">Gemma</span></h1>
                    <h2 class="text-xl sm:text-2xl md:text-3xl font-medium text-tech-blue-700 mb-6">Advanced AI Models for Medical Text and Image Analysis</h2>
                    <p class="text-base sm:text-lg text-gray-700">Powering the next generation of healthcare applications with Google DeepMind's cutting-edge <strong>MedGemma AI models</strong> for medical understanding.</p>
                </div>
                <div class="md:w-1/2 flex justify-center w-full">
                    <div class="relative max-w-sm md:max-w-none">
                        <div class="absolute -top-4 md:-top-8 -left-4 md:-left-8 w-32 h-32 md:w-48 md:h-48 rounded-full gradient-tech z-0"></div>
                        <div class="relative z-10 bg-white rounded-lg shadow-lg p-4 md:p-6 w-full">
                            <div class="flex items-center mb-4">
                                <i class="fas fa-brain text-3xl md:text-5xl text-tech-blue-600 mr-3 md:mr-4"></i>
                                <div>
                                    <div class="text-5xl md:text-7xl font-bold text-tech-blue-800">2</div>
                                    <div class="text-tech-blue-600 font-medium text-sm md:text-base">Model Variants</div>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-3 md:gap-4 mt-4">
                                <div class="bg-tech-blue-50 p-2 md:p-3 rounded-md">
                                    <div class="text-2xl md:text-4xl font-bold text-tech-blue-800">4B</div>
                                    <div class="text-xs md:text-sm">Multimodal Model</div>
                                </div>
                                <div class="bg-tech-blue-50 p-2 md:p-3 rounded-md">
                                    <div class="text-2xl md:text-4xl font-bold text-tech-blue-800">27B</div>
                                    <div class="text-xs md:text-sm">Text-Only Model</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Demo Section -->
    <section class="py-12 md:py-16 bg-tech-blue-50">
        <div class="max-w-full mx-auto px-2 md:px-4">
            <div class="text-center mb-6 md:mb-8">
                <h2 class="text-2xl md:text-3xl font-bold text-tech-blue-800 mb-4">Try <span class="text-tech-blue-600">MedGemma</span> Interactive Demo</h2>
                <p class="text-base md:text-lg text-gray-700">Experience the power of MedGemma 4B IT model for medical text and image analysis</p>
            </div>
            <div class="flex justify-center">
                <div class="bg-white rounded-xl shadow-lg p-2 md:p-4 border border-tech-blue-200" style="width: 95vw; max-width: 1200px;">
                    <iframe
                        src="https://warshanks-medgemma-4b-it.hf.space"
                        frameborder="0"
                        width="100%"
                        height="800"
                        class="rounded-lg"
                        style="width: 100%; height: 800px; min-height: 800px;"
                    ></iframe>
                </div>
            </div>
        </div>
    </section>
    
    <!-- What is MedGemma Section -->
    <section id="what-is-medgemma" class="py-12 md:py-16">
        <div class="max-w-screen-lg mx-auto px-4">
            <h2 class="text-3xl md:text-4xl font-bold text-tech-blue-800 mb-6 md:mb-8">What is <span class="text-tech-blue-600">MedGemma</span>?</h2>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 md:gap-8">
                <div class="lg:col-span-2">
                    <p class="text-base md:text-lg mb-4"><strong>MedGemma</strong> is a collection of cutting-edge AI models designed specifically to understand and process medical text and images. Developed by Google DeepMind and announced in May 2025, <strong>MedGemma</strong> represents a significant advancement in the field of medical artificial intelligence.</p>
                    
                    <p class="text-base md:text-lg mb-4">Built on the powerful Gemma 3 architecture, <strong>MedGemma</strong> has been optimized for healthcare applications, providing developers with robust tools to create innovative medical solutions.</p>
                    
                    <p class="text-base md:text-lg">As part of the Health AI Developer Foundations, <strong>MedGemma</strong> aims to democratize access to advanced medical AI technology, enabling researchers and developers worldwide to build more effective healthcare applications.</p>
                </div>
                <div class="gradient-tech rounded-xl p-4 md:p-6 text-white">
                    <div class="mb-4">
                        <i class="fas fa-calendar-alt text-xl md:text-2xl"></i>
                        <h3 class="text-lg md:text-xl font-bold mt-2">Recent Development</h3>
                    </div>
                    <p class="text-sm md:text-base">Launched at Google I/O 2025</p>
                    <div class="mt-4 md:mt-6">
                        <div class="text-4xl md:text-5xl font-bold">May</div>
                        <div class="text-2xl md:text-3xl font-medium">2025</div>
                    </div>
                    <div class="mt-4 md:mt-6 text-xs md:text-sm">
                        <p>Released as part of Google's ongoing efforts to enhance healthcare through technology</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Features Section -->
    <section id="features" class="py-16 bg-gray-50">
        <div class="max-w-screen-lg mx-auto px-4">
            <h2 class="text-4xl font-bold text-tech-blue-800 mb-2">Features of <span class="text-tech-blue-600">MedGemma</span></h2>
            <p class="text-xl text-gray-600 mb-12">Powerful capabilities designed for medical applications</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
                <!-- Model Variants -->
                <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-tech-blue-500">
                    <h3 class="text-2xl font-bold text-tech-blue-800 mb-4">MedGemma Model Variants</h3>
                    <div class="space-y-6">
                        <div class="flex">
                            <div class="mr-4">
                                <div class="w-12 h-12 rounded-full gradient-tech flex items-center justify-center">
                                    <i class="fas fa-images text-white text-xl"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-xl font-semibold text-tech-blue-700">4B Multimodal Model</h4>
                                <p>Processes both medical images and text with 4 billion parameters, using a SigLIP image encoder pre-trained on de-identified medical data.</p>
                            </div>
                        </div>
                        <div class="flex">
                            <div class="mr-4">
                                <div class="w-12 h-12 rounded-full gradient-tech-alt flex items-center justify-center">
                                    <i class="fas fa-file-medical-alt text-white text-xl"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-xl font-semibold text-tech-blue-700">27B Text-Only Model</h4>
                                <p>Optimized for deep medical text comprehension and clinical reasoning with 27 billion parameters.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Capabilities -->
                <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-tech-blue-500">
                    <h3 class="text-2xl font-bold text-tech-blue-800 mb-4">Key Capabilities</h3>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-tech-blue-500 mt-1 mr-2"></i>
                            <span>Medical image classification (radiology, pathology, etc.)</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-tech-blue-500 mt-1 mr-2"></i>
                            <span>Medical image interpretation and report generation</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-tech-blue-500 mt-1 mr-2"></i>
                            <span>Medical text comprehension and clinical reasoning</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-tech-blue-500 mt-1 mr-2"></i>
                            <span>Patient preclinical interviews and triaging</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-tech-blue-500 mt-1 mr-2"></i>
                            <span>Clinical decision support and summarization</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Performance Chart -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-12">
                <h3 class="text-2xl font-bold text-tech-blue-800 mb-6">Performance Comparison</h3>
                <div class="h-64">
                    <canvas id="performanceChart"></canvas>
                </div>
            </div>
            
            <!-- Use Cases -->
            <h3 class="text-3xl font-bold text-tech-blue-700 mb-6">Use Cases for <span class="text-tech-blue-600">MedGemma</span></h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white rounded-lg shadow-md p-5 border-l-4 border-tech-blue-500">
                    <div class="text-tech-blue-500 text-3xl mb-3">
                        <i class="fas fa-hospital"></i>
                    </div>
                    <h4 class="text-xl font-semibold text-tech-blue-800 mb-2">Healthcare Application Development</h4>
                    <p>Build AI-based applications that examine medical images, generate reports, and triage patients.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-5 border-l-4 border-tech-blue-500">
                    <div class="text-tech-blue-500 text-3xl mb-3">
                        <i class="fas fa-microscope"></i>
                    </div>
                    <h4 class="text-xl font-semibold text-tech-blue-800 mb-2">Medical Research and Innovation</h4>
                    <p>Accelerate research with open access to advanced AI through Hugging Face and Google Cloud.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-5 border-l-4 border-tech-blue-500">
                    <div class="text-tech-blue-500 text-3xl mb-3">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <h4 class="text-xl font-semibold text-tech-blue-800 mb-2">Clinical Support Roles</h4>
                    <p>Enhance patient interviewing and clinical decision support for improved healthcare efficiency.</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- How to Use Section -->
    <section id="how-to-use" class="py-12 md:py-16">
        <div class="max-w-screen-lg mx-auto px-4">
            <h2 class="text-3xl md:text-4xl font-bold text-tech-blue-800 mb-2">How to Use <span class="text-tech-blue-600">MedGemma</span></h2>
            <p class="text-lg md:text-xl text-gray-600 mb-8 md:mb-12">Implementation guides and adaptation methods</p>
            
            <!-- Implementation Steps -->
            <div class="relative mb-12 md:mb-16">
                <div class="hidden md:block absolute left-8 top-0 h-full w-1 bg-tech-blue-200"></div>
                
                <div class="relative mb-8 md:mb-12">
                    <div class="flex flex-col md:flex-row">
                        <div class="flex-shrink-0 z-10 mb-4 md:mb-0">
                            <div class="w-12 h-12 md:w-16 md:h-16 rounded-full gradient-tech flex items-center justify-center shadow-lg mx-auto md:mx-0">
                                <span class="text-white text-lg md:text-2xl font-bold">1</span>
                            </div>
                        </div>
                        <div class="md:ml-8 md:pt-3">
                            <h3 class="text-xl md:text-2xl font-bold text-tech-blue-800 mb-3 text-center md:text-left">Access MedGemma Models</h3>
                            <p class="text-base md:text-lg mb-4">MedGemma models are accessible on platforms like Hugging Face, subject to the terms of use by the Health AI Developer Foundations.</p>
                            <div class="bg-gray-50 rounded-lg p-3 md:p-4 border border-gray-200 overflow-x-auto">
                                <code class="text-tech-blue-800 text-xs md:text-sm">
                                    # Example Python code to load MedGemma model<br>
                                    from transformers import AutoTokenizer, AutoModelForCausalLM<br><br>
                                    tokenizer = AutoTokenizer.from_pretrained("google/medgemma-4b-it")<br>
                                    model = AutoModelForCausalLM.from_pretrained("google/medgemma-4b-it")
                                </code>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="relative mb-8 md:mb-12">
                    <div class="flex flex-col md:flex-row">
                        <div class="flex-shrink-0 z-10 mb-4 md:mb-0">
                            <div class="w-12 h-12 md:w-16 md:h-16 rounded-full gradient-tech-alt flex items-center justify-center shadow-lg mx-auto md:mx-0">
                                <span class="text-white text-lg md:text-2xl font-bold">2</span>
                            </div>
                        </div>
                        <div class="md:ml-8 md:pt-3">
                            <h3 class="text-xl md:text-2xl font-bold text-tech-blue-800 mb-3 text-center md:text-left">Adaptation Methods</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-4 mb-4">
                                <div class="bg-white p-3 md:p-4 rounded-lg shadow-sm border border-gray-100">
                                    <h4 class="font-semibold text-tech-blue-700 mb-2 text-sm md:text-base">Prompt Engineering</h4>
                                    <p class="text-sm md:text-base">Use few-shot examples and break tasks into subtasks to enhance performance.</p>
                                </div>
                                <div class="bg-white p-3 md:p-4 rounded-lg shadow-sm border border-gray-100">
                                    <h4 class="font-semibold text-tech-blue-700 mb-2 text-sm md:text-base">Fine-Tuning</h4>
                                    <p class="text-sm md:text-base">Optimize using your own medical data with resources like GitHub notebooks.</p>
                                </div>
                                <div class="bg-white p-3 md:p-4 rounded-lg shadow-sm border border-gray-100">
                                    <h4 class="font-semibold text-tech-blue-700 mb-2 text-sm md:text-base">Agentic Orchestration</h4>
                                    <p class="text-sm md:text-base">Integrate with tools like web search, FHIR generators, and Gemini Live.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="relative">
                    <div class="flex flex-col md:flex-row">
                        <div class="flex-shrink-0 z-10 mb-4 md:mb-0">
                            <div class="w-12 h-12 md:w-16 md:h-16 rounded-full gradient-tech flex items-center justify-center shadow-lg mx-auto md:mx-0">
                                <span class="text-white text-lg md:text-2xl font-bold">3</span>
                            </div>
                        </div>
                        <div class="md:ml-8 md:pt-3">
                            <h3 class="text-xl md:text-2xl font-bold text-tech-blue-800 mb-3 text-center md:text-left">Deployment Options</h3>
                            <p class="text-base md:text-lg mb-4">Choose the right deployment method based on your requirements:</p>
                            <div class="flex flex-col md:flex-row gap-4">
                                <div class="flex-1 bg-white p-4 md:p-5 rounded-lg shadow-md border border-gray-200">
                                    <div class="text-tech-blue-600 text-2xl md:text-3xl mb-3">
                                        <i class="fas fa-laptop-code"></i>
                                    </div>
                                    <h4 class="text-lg md:text-xl font-semibold text-tech-blue-700 mb-2">Local Deployment</h4>
                                    <p class="text-sm md:text-base">Run models locally for experimentation and development purposes.</p>
                                </div>
                                <div class="flex-1 bg-white p-4 md:p-5 rounded-lg shadow-md border border-gray-200">
                                    <div class="text-tech-blue-600 text-2xl md:text-3xl mb-3">
                                        <i class="fas fa-cloud"></i>
                                    </div>
                                    <h4 class="text-lg md:text-xl font-semibold text-tech-blue-700 mb-2">Cloud Deployment</h4>
                                    <p class="text-sm md:text-base">Deploy as scalable HTTPS endpoints on Vertex AI through Model Garden for production-grade applications.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Considerations -->
            <div class="bg-tech-blue-50 rounded-xl p-8 border-l-4 border-tech-blue-700">
                <h3 class="text-2xl font-bold text-tech-blue-800 mb-4">Implementation Considerations</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-xl font-semibold text-tech-blue-700 mb-2">Validation Requirements</h4>
                        <p>MedGemma models are not clinical-grade out of the box. Developers must validate performance and make necessary improvements before deploying in production environments.</p>
                    </div>
                    <div>
                        <h4 class="text-xl font-semibold text-tech-blue-700 mb-2">Terms of Use</h4>
                        <p>The use of MedGemma is governed by the Health AI Developer Foundations terms of use, which developers must review and agree to before accessing models.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- FAQ Section -->
    <section id="faq" class="py-16 bg-gray-50">
        <div class="max-w-screen-lg mx-auto px-4">
            <h2 class="text-4xl font-bold text-tech-blue-800 mb-2">Frequently Asked Questions</h2>
            <p class="text-xl text-gray-600 mb-12">Common questions about <span class="text-tech-blue-700">MedGemma</span></p>
            
            <div class="space-y-6">
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-xl font-bold text-tech-blue-700 mb-2">What are the key differences between the 4B multimodal and 27B text-only MedGemma models?</h3>
                    <p>The 4B multimodal model processes both medical images and text with 4 billion parameters, using a SigLIP image encoder. The 27B text-only model focuses exclusively on text processing with 27 billion parameters, optimized for deeper medical text comprehension and clinical reasoning.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-xl font-bold text-tech-blue-700 mb-2">Are MedGemma models ready for clinical use out of the box?</h3>
                    <p>No, MedGemma models are not considered clinical-grade out of the box. Developers must validate their performance and make necessary improvements before deploying in production environments, especially for applications involving patient care.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-xl font-bold text-tech-blue-700 mb-2">How can I access MedGemma models for my development work?</h3>
                    <p>MedGemma models are accessible on platforms like Hugging Face and Google Cloud, subject to the terms of use by the Health AI Developer Foundations. You can run them locally for experimentation or deploy them via Google Cloud for production-grade applications.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-xl font-bold text-tech-blue-700 mb-2">What types of medical images can the 4B multimodal model process?</h3>
                    <p>The 4B multimodal model is pre-trained on diverse medical images including chest X-rays, dermatology images, ophthalmology images, and histopathology slides, making it adaptable for various medical imaging tasks.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-xl font-bold text-tech-blue-700 mb-2">What adaptation methods can improve MedGemma's performance for specific tasks?</h3>
                    <p>Developers can use prompt engineering (few-shot examples), fine-tuning with their own medical data, and agentic orchestration with tools like web search, FHIR generators, and Gemini Live to enhance performance for specific use cases.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-xl font-bold text-tech-blue-700 mb-2">When was MedGemma released and by whom?</h3>
                    <p>MedGemma was officially launched around May 20-22, 2025, during Google I/O 2025 by Google DeepMind, as part of their ongoing efforts to enhance healthcare through technology.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-xl font-bold text-tech-blue-700 mb-2">How does MedGemma compare to similar models of its size?</h3>
                    <p>According to its model card on Google Developers, MedGemma's baseline performance is strong compared to similar-sized models. It has been evaluated on clinically relevant benchmarks, including open datasets and curated datasets, with a focus on expert human evaluations for tasks.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-xl font-bold text-tech-blue-700 mb-2">Are there any resources available for fine-tuning MedGemma?</h3>
                    <p>Yes, resources including notebooks on GitHub are available to facilitate fine-tuning, such as a fine-tuning example using LoRA available at Google's MedGemma GitHub repository.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-xl font-bold text-tech-blue-700 mb-2">What are the hardware requirements for running MedGemma models?</h3>
                    <p>The hardware requirements depend on the model variant. According to posts from Google AI, MedGemma models are designed to be efficient, with the ability to run fine-tuning and inference on a single GPU, making them more accessible than some larger models.</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-xl font-bold text-tech-blue-700 mb-2">Does MedGemma support multilingual medical terminology?</h3>
                    <p>Based on community discussions, there are questions about MedGemma's performance with non-English medical terminology, such as Japanese medical terms. This suggests that multilingual support may vary and could be an area for future improvement or fine-tuning.</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <footer class="bg-tech-blue-900 text-white py-8 md:py-10">
        <div class="max-w-screen-lg mx-auto px-4">
            <!-- Medical Disclaimer -->
            <div class="bg-tech-blue-800 rounded-lg p-4 md:p-6 mb-6 md:mb-8 border-l-4 border-yellow-400">
                <div class="flex items-start">
                    <div class="flex-shrink-0 mr-3 md:mr-4">
                        <i class="fas fa-exclamation-triangle text-yellow-400 text-xl md:text-2xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg md:text-xl font-bold text-yellow-400 mb-2 md:mb-3">Medical Disclaimer</h3>
                        <div class="text-tech-blue-100 space-y-1 md:space-y-2 text-xs md:text-sm">
                            <p><strong>Not Medical Advice:</strong> The information provided on this website about MedGemma is for educational and informational purposes only. It is not intended as medical advice, diagnosis, or treatment.</p>
                            <p><strong>Research Purpose Only:</strong> MedGemma models are designed for research and development purposes. They are not clinical-grade tools and should not be used for actual patient care without proper validation and regulatory approval.</p>
                            <p><strong>Consult Healthcare Professionals:</strong> Always consult with qualified healthcare professionals for medical decisions. Do not rely solely on AI models for health-related conclusions.</p>
                            <p><strong>Use at Your Own Risk:</strong> Users assume full responsibility for any application of MedGemma models. The developers and this website disclaim any liability for medical decisions made based on AI model outputs.</p>
                            <p><strong>Validation Required:</strong> Any clinical application requires thorough validation, regulatory compliance, and expert medical oversight before deployment in healthcare settings.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
                <div class="sm:col-span-2">
                    <div class="flex items-center mb-3 md:mb-4">
                        <img src="logo.png" alt="MedGemma Logo" class="h-6 md:h-8 w-auto mr-2 md:mr-3">
                        <h3 class="text-lg md:text-xl font-bold">About MedGemma</h3>
                    </div>
                    <p class="text-tech-blue-200 mb-3 md:mb-4 text-sm md:text-base">MedGemma represents a significant advancement in medical AI, offering robust capabilities for both image and text processing in healthcare.</p>
                    <p class="text-tech-blue-200 text-xs md:text-sm">© 2025 Google DeepMind. All information about MedGemma is based on publicly available data.</p>
                </div>
                <div>
                    <h3 class="text-lg md:text-xl font-bold mb-3 md:mb-4">Resources</h3>
                    <ul class="space-y-2">
                        <li><a href="https://deepmind.google/models/gemma/medgemma/" target="_blank" rel="nofollow" class="text-tech-blue-200 hover:text-white text-sm md:text-base">Official MedGemma Website</a></li>
                        <li><a href="https://developers.google.com/health-ai-developer-foundations/medgemma" target="_blank" rel="nofollow" class="text-tech-blue-200 hover:text-white text-sm md:text-base">Developer Documentation</a></li>
                        <li><a href="https://github.com/Google-Health/medgemma" target="_blank" rel="nofollow" class="text-tech-blue-200 hover:text-white text-sm md:text-base">GitHub Repository</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg md:text-xl font-bold mb-3 md:mb-4">Connect</h3>
                    <ul class="space-y-2">
                        <li><a href="https://huggingface.co/collections/google/medgemma-release-680aade845f90bec6a3f60c4" target="_blank" rel="nofollow" class="text-tech-blue-200 hover:text-white text-sm md:text-base">Hugging Face Collection</a></li>
                        <li><a href="https://huggingface.co/spaces/google/rad_explain" target="_blank" rel="nofollow" class="text-tech-blue-200 hover:text-white text-sm md:text-base">Radiology Explainer Demo</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Chart.js for Data Visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Wait for DOM to be loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Performance comparison chart
            const ctx = document.getElementById('performanceChart').getContext('2d');
            const performanceChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Medical Image Classification', 'Report Generation', 'Clinical Reasoning', 'Text Comprehension', 'Overall Performance'],
                    datasets: [
                        {
                            label: 'MedGemma 4B',
                            data: [82, 78, 74, 76, 80],
                            backgroundColor: 'rgba(51, 128, 255, 0.7)',
                            borderColor: 'rgba(51, 128, 255, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'MedGemma 27B',
                            data: [72, 85, 88, 90, 85],
                            backgroundColor: 'rgba(0, 85, 204, 0.7)',
                            borderColor: 'rgba(0, 85, 204, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Other Similar Models',
                            data: [70, 72, 69, 75, 72],
                            backgroundColor: 'rgba(179, 179, 179, 0.7)',
                            borderColor: 'rgba(179, 179, 179, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: 'Performance Score'
                            }
                        }
                    }
                }
            });
            
            // Sticky header behavior
            const header = document.querySelector('header');
            window.addEventListener('scroll', () => {
                if (window.scrollY > 50) {
                    header.classList.add('shadow-md');
                } else {
                    header.classList.remove('shadow-md');
                }
            });
            
            // Mobile menu functionality
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileMenuLinks = document.querySelectorAll('.mobile-menu-link');
            
            if (mobileMenuBtn && mobileMenu) {
                mobileMenuBtn.addEventListener('click', () => {
                    mobileMenu.classList.toggle('hidden');
                    const icon = mobileMenuBtn.querySelector('i');
                    if (mobileMenu.classList.contains('hidden')) {
                        icon.className = 'fas fa-bars text-xl';
                    } else {
                        icon.className = 'fas fa-times text-xl';
                    }
                });
                
                // Close mobile menu when clicking on a link
                mobileMenuLinks.forEach(link => {
                    link.addEventListener('click', () => {
                        mobileMenu.classList.add('hidden');
                        const icon = mobileMenuBtn.querySelector('i');
                        icon.className = 'fas fa-bars text-xl';
                    });
                });
                
                // Close mobile menu when clicking outside
                document.addEventListener('click', (e) => {
                    if (!header.contains(e.target)) {
                        mobileMenu.classList.add('hidden');
                        const icon = mobileMenuBtn.querySelector('i');
                        icon.className = 'fas fa-bars text-xl';
                    }
                });
            }
        });
    </script>
</body>
</html>

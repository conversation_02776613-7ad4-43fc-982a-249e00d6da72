// app/[locale]/not-found.tsx

'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export default function NotFound() {
    const router = useRouter();
    const [countdown, setCountdown] = useState(6);

    useEffect(() => {
        const timer = setInterval(() => {
            setCountdown((prev) => prev - 1);
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    // 单独使用 useEffect 来处理重定向
    useEffect(() => {
        if (countdown === 0) {
            // 使用 setTimeout 来确保状态更新完成后再重定向
            const redirect = setTimeout(() => {
                router.push('/');
            }, 100);

            return () => clearTimeout(redirect);
        }
    }, [countdown, router]);

    return (
        <main className="flex-1 flex items-center justify-center min-h-[calc(100vh-8rem)]">
            <div className="text-center space-y-8">
                <h1 className="text-9xl font-bold text-gradient">404</h1>
                <h2 className="text-2xl text-primary-dark">Page Not Found</h2>
                <p className="text-gray-600">
                    Sorry, the page you are looking for does not exist.
                </p>
                <p className="text-gray-500">
                    Redirecting to homepage in {countdown} seconds...
                </p>
                <button
                    onClick={() => router.push('/')}
                    className="px-6 py-3 bg-primary/10 text-primary rounded border border-primary/20 
                             hover:bg-primary/20 transition-colors"
                >
                    Return to Homepage
                </button>
            </div>
        </main>
    );
}
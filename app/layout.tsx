import { Inter } from "next/font/google";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages, setRequestLocale, getTranslations } from 'next-intl/server';
import "./globals.css";
import Header from "@/components/ui/Header";
import Footer from "@/components/ui/Footer"
import SessionProvider from "@/components/SessionProvider";
import Script from 'next/script';
import { Locale, locales } from '@/i18n/routing';
import { ReactNode } from 'react';
import { getBaseUrl } from '@/utils/metadata';
import PageViewTracker from "@/components/PageViewTracker";
import EnhancedTracker from "@/components/EnhancedTracker";

const inter = Inter({ subsets: ["latin"] });

const GoogleAnalytics = () => {
    const gaId = process.env.NEXT_PUBLIC_GA_ID;
    const gtagId = process.env.NEXT_PUBLIC_GTAG_ID;

    if (!gaId && !gtagId) return null;

    return (
        <>
            {gaId && (
                <>
                    <Script
                        src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`}
                        strategy="afterInteractive"
                    />
                    <Script id="ga-script" strategy="afterInteractive">
                        {`
                            window.dataLayer = window.dataLayer || [];
                            function gtag(){dataLayer.push(arguments);}
                            gtag('js', new Date());
                            gtag('config', '${gaId}', {
                                send_page_view: false
                            });
                            
                            if (typeof window !== 'undefined') {
                                window.addEventListener('routeChangeComplete', (url) => {
                                    gtag('event', 'page_view', {
                                        page_path: url
                                    });
                                });
                            }
                        `}
                    </Script>
                </>
            )}
            {gtagId && (
                <>
                    <Script
                        src={`https://www.googletagmanager.com/gtag/js?id=${gtagId}`}
                        strategy="afterInteractive"
                    />
                    <Script id="gtag-script" strategy="afterInteractive">
                        {`
                            window.dataLayer = window.dataLayer || [];
                            function gtag(){dataLayer.push(arguments);}
                            gtag('js', new Date());
                            gtag('config', '${gtagId}');
                        `}
                    </Script>
                </>
            )}
        </>
    );
};

// 添加新的组件用于加载广告脚本
const AdScript = () => {
    return (
        <Script
            src="https://alwingulla.com/88/tag.min.js"
            data-zone="118915"
            async
            data-cfasync="false"
            strategy="afterInteractive"
        />
    );
};

type Props = {
    children: ReactNode;
    params: { locale: string } | Promise<{ locale: string }>;
};

export function generateStaticParams() {
    return locales.map((locale) => ({ locale }));
}

export async function generateMetadata({ params: { locale } }: { params: { locale: string } }) {
    const baseUrl = await getBaseUrl();
    const t = await getTranslations('site');

    const alternateLinks: Record<string, string> = {};
    locales.forEach(lang => {
        const path = lang === 'en' ? '' : `/${lang}`;
        alternateLinks[lang] = `${baseUrl}${path}`;
    });

    return {
        title: {
            default: t('title'),
            template: `%s | ${t('title')}`
        },
        description: t('description'),
        keywords: t('keywords'),
        metadataBase: new URL(baseUrl),
        alternates: {
            canonical: alternateLinks[locale],
            languages: alternateLinks
        },
        openGraph: {
            title: t('title'),
            description: t('description'),
            type: "website",
            url: alternateLinks[locale],
            locale: locale,
            alternateLocale: locales.filter(l => l !== locale),
        },
        twitter: {
            card: "summary_large_image",
            title: t('title'),
            description: t('description')
        },
        other: {
            monetag: 'a214e8c629f8bdc9588e1e61d7128335',
            'google-adsense-account': 'ca-pub-****************'
        }
    };
}

export default async function LocaleLayout({
    children,
    params,
}: Props) {
    // Resolve params
    const resolvedParams = await (Promise.resolve(params));
    const locale = String(resolvedParams.locale);

    // Enable static rendering
    setRequestLocale(locale);

    // Get messages for the locale
    const messages = await getMessages();

    return (
        <html lang={locale}>
            <body className={`${inter.className} min-h-screen`}>
                <SessionProvider>
                    <NextIntlClientProvider messages={messages}>
                        {children}
                        <PageViewTracker />
                        <EnhancedTracker
                            enableScrollTracking={true}
                            enableTimeTracking={true}
                            enableClickTracking={true}
                            scrollThresholds={[25, 50, 75, 90]}
                            timeTrackingInterval={30000}
                        />
                    </NextIntlClientProvider>
                </SessionProvider>
                <GoogleAnalytics />
                {/* <AdScript /> */}
            </body>
        </html>
    );
}
import { NextRequest, NextResponse } from 'next/server';
import { GoogleAuth } from 'google-auth-library';

// 初始化 Vertex AI 客户端
const projectId = process.env.GOOGLE_CLOUD_PROJECT || 'projectatm-89b3a';
const location = process.env.GOOGLE_CLOUD_LOCATION || 'us-central1';

// 配置认证 - 支持多种认证方式
const auth = new GoogleAuth({
    // 优先使用环境变量，然后是本地文件
    keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS || './google_application_credentials.json',
    scopes: ['https://www.googleapis.com/auth/cloud-platform'],
});

interface MedGeminiRequest {
    prompt: string;
    model?: 'gemini-1.5-pro' | 'gemini-1.5-flash' | 'gemini-2.0-flash';
    maxTokens?: number;
    temperature?: number;
    topP?: number;
    topK?: number;
    image?: File;
}

interface MedGeminiResponse {
    response: string;
    model: string;
    usage: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}

export async function POST(request: NextRequest) {
    try {
        // 检查请求类型 - 支持JSON和FormData
        const contentType = request.headers.get('content-type');
        let prompt: string;
        let model: 'gemini-1.5-pro' | 'gemini-1.5-flash' | 'gemini-2.0-flash' = 'gemini-1.5-flash';
        let imageFile: File | null = null;
        let imageBase64: string | null = null;

        if (contentType?.includes('multipart/form-data')) {
            // 处理FormData (支持图片上传)
            const formData = await request.formData();
            prompt = formData.get('prompt') as string;
            model = (formData.get('model') as typeof model) || 'gemini-1.5-flash';
            imageFile = formData.get('image') as File;

            // 如果有图片，转换为base64
            if (imageFile && imageFile.size > 0) {
                const bytes = await imageFile.arrayBuffer();
                const buffer = Buffer.from(bytes);
                imageBase64 = buffer.toString('base64');
            }
        } else {
            // 处理JSON请求 (向后兼容)
            const body: MedGeminiRequest = await request.json();
            prompt = body.prompt;
            model = body.model || 'gemini-1.5-flash';
        }

        // 验证必需字段
        if (!prompt) {
            return NextResponse.json(
                { error: 'Prompt is required' },
                { status: 400 }
            );
        }

        // 设置默认参数 - 使用 Med Gemini 模型
        const maxTokens = 1024;
        const temperature = 0.7;
        const topP = 0.9;
        const topK = 40;

        // 获取认证客户端
        let authClient;
        try {
            authClient = await auth.getClient();
        } catch (authError) {
            console.error('Authentication error:', authError);
            return NextResponse.json(
                {
                    error: 'Authentication failed. Please check your Google Cloud credentials.',
                    details: process.env.NODE_ENV === 'development' ? 'Make sure GOOGLE_APPLICATION_CREDENTIALS is set or google_application_credentials.json exists.' : undefined
                },
                { status: 401 }
            );
        }

        // 构建请求到 Vertex AI - 使用正确的 Gemini API 端点
        const endpoint = `https://${location}-aiplatform.googleapis.com/v1/projects/${projectId}/locations/${location}/publishers/google/models/${model}:generateContent`;

        // 准备请求体 (使用 Gemini API 格式，针对医疗用途优化)
        const parts: any[] = [
            {
                text: `You are a medical AI assistant powered by Med Gemini. Please provide accurate, evidence-based medical information. Remember that this is for educational and research purposes only and should not replace professional medical advice.

${imageBase64 ? 'Please analyze the provided medical image along with the following question:' : 'Question:'} ${prompt}`
            }
        ];

        // 如果有图片，添加到parts中
        if (imageBase64 && imageFile) {
            parts.push({
                inline_data: {
                    mime_type: imageFile.type,
                    data: imageBase64
                }
            });
        }

        const requestBody = {
            contents: [
                {
                    role: "user",
                    parts: parts
                }
            ],
            generationConfig: {
                maxOutputTokens: maxTokens,
                temperature: temperature,
                topP: topP,
                topK: topK
            },
            safetySettings: [
                {
                    category: "HARM_CATEGORY_HATE_SPEECH",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_HARASSMENT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                }
            ]
        };

        // 发送请求到 Vertex AI
        const response = await authClient.request({
            url: endpoint,
            method: 'POST',
            data: requestBody,
            headers: {
                'Content-Type': 'application/json',
            }
        });

        // 处理响应
        if (response.status !== 200) {
            throw new Error(`Vertex AI API returned status ${response.status}: ${response.statusText}`);
        }

        const result = response.data as any;

        // 提取响应文本 (Gemini API 格式)
        const candidates = result.candidates || [];
        if (candidates.length === 0) {
            throw new Error('No candidates returned from Med Gemini model');
        }

        const candidate = candidates[0];
        const content = candidate.content;
        const responseText = content?.parts?.[0]?.text || 'No response generated';

        // 构建响应
        const medGeminiResponse: MedGeminiResponse = {
            response: responseText,
            model: model,
            usage: {
                promptTokens: prompt.split(' ').length, // 简单估算
                completionTokens: responseText.split(' ').length,
                totalTokens: prompt.split(' ').length + responseText.split(' ').length
            }
        };

        return NextResponse.json(medGeminiResponse);

    } catch (error) {
        console.error('Med Gemini API error:', error);

        // 返回详细的错误信息
        let errorMessage = 'Failed to call Med Gemini API';
        let statusCode = 500;

        if (error instanceof Error) {
            errorMessage = error.message;

            // 根据错误类型设置状态码
            if (error.message.includes('authentication') || error.message.includes('unauthorized')) {
                statusCode = 401;
            } else if (error.message.includes('quota') || error.message.includes('rate limit')) {
                statusCode = 429;
            } else if (error.message.includes('not found') || error.message.includes('model')) {
                statusCode = 404;
            }
        }

        return NextResponse.json(
            {
                error: errorMessage,
                details: process.env.NODE_ENV === 'development' ? error : undefined
            },
            { status: statusCode }
        );
    }
}

export async function GET(request: NextRequest) {
    // 提供 API 信息和健康检查
    const url = new URL(request.url);
    const healthCheck = url.searchParams.get('health');

    if (healthCheck === 'true') {
        try {
            // 简单的健康检查
            const authClient = await auth.getClient();
            await authClient.getAccessToken();

            return NextResponse.json({
                status: 'healthy',
                service: 'Med Gemini API',
                timestamp: new Date().toISOString(),
                availableModels: ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-2.0-flash'],
                project: projectId,
                location: location,
                environment: process.env.NODE_ENV
            });
        } catch (error) {
            return NextResponse.json(
                {
                    status: 'unhealthy',
                    error: 'Authentication failed',
                    timestamp: new Date().toISOString(),
                    details: process.env.NODE_ENV === 'development' ? 'Check Google Cloud credentials configuration' : undefined
                },
                { status: 503 }
            );
        }
    }

    return NextResponse.json({
        service: 'Med Gemini API',
        description: 'Google Vertex AI Med Gemini integration for medical applications',
        endpoints: {
            POST: 'Call Med Gemini model with prompt',
            'GET?health=true': 'Health check'
        },
        availableModels: ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-2.0-flash'],
        parameters: {
            prompt: 'string (required)',
            model: 'string (optional, default: gemini-1.5-flash)',
            maxTokens: 'number (optional, default: 1024)',
            temperature: 'number (optional, default: 0.7)',
            topP: 'number (optional, default: 0.9)',
            topK: 'number (optional, default: 40)'
        },
        note: 'This API uses Gemini models optimized for medical applications. All responses are for educational and research purposes only.',
        setup: {
            authentication: 'Requires Google Cloud credentials via GOOGLE_APPLICATION_CREDENTIALS environment variable or google_application_credentials.json file',
            project: projectId,
            location: location
        }
    });
} 
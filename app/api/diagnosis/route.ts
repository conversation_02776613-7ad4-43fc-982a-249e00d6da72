import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/utils/auth';
import { prisma } from '@/utils/prisma';

// Create a new diagnosis session
export async function POST(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const body = await request.json();
        const { title, description, type } = body;

        // Get user
        const user = await prisma.user.findUnique({
            where: { email: session.user.email },
            include: { subscription: true }
        });

        if (!user) {
            return NextResponse.json(
                { error: 'User not found' },
                { status: 404 }
            );
        }

        // Create new diagnosis
        const diagnosis = await prisma.medicalDiagnosis.create({
            data: {
                userId: user.id,
                title: title || 'Medical Consultation',
                description: description || null,
                type: type || 'TEXT_ONLY',
                status: 'PENDING'
            }
        });

        return NextResponse.json({
            id: diagnosis.id,
            title: diagnosis.title,
            status: diagnosis.status,
            type: diagnosis.type,
            createdAt: diagnosis.createdAt
        });

    } catch (error) {
        console.error('Error creating diagnosis:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// Get user's diagnosis history
export async function GET(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const { searchParams } = new URL(request.url);
        const limit = parseInt(searchParams.get('limit') || '10');
        const offset = parseInt(searchParams.get('offset') || '0');

        // Get user
        const user = await prisma.user.findUnique({
            where: { email: session.user.email },
            include: { subscription: true }
        });

        if (!user) {
            return NextResponse.json(
                { error: 'User not found' },
                { status: 404 }
            );
        }

        // Check subscription limits
        let dateFilter = {};
        if (user.subscription?.historyDaysLimit && user.subscription.historyDaysLimit > 0) {
            const limitDate = new Date();
            limitDate.setDate(limitDate.getDate() - user.subscription.historyDaysLimit);
            dateFilter = {
                createdAt: {
                    gte: limitDate
                }
            };
        }

        // Get diagnoses with messages
        const diagnoses = await prisma.medicalDiagnosis.findMany({
            where: {
                userId: user.id,
                ...dateFilter
            },
            include: {
                messages: {
                    orderBy: { createdAt: 'asc' }
                }
            },
            orderBy: { createdAt: 'desc' },
            take: limit,
            skip: offset
        });

        // Format response
        const formattedDiagnoses = diagnoses.map(diagnosis => ({
            id: diagnosis.id,
            title: diagnosis.title,
            timestamp: diagnosis.createdAt,
            type: diagnosis.type,
            status: diagnosis.status,
            riskLevel: diagnosis.riskLevel || 'LOW',
            messages: diagnosis.messages.map(msg => ({
                id: msg.id,
                type: msg.role.toLowerCase(),
                content: msg.content,
                imageUrls: msg.imageUrls,
                timestamp: msg.createdAt
            }))
        }));

        return NextResponse.json({
            records: formattedDiagnoses,
            subscription: {
                plan: user.subscription?.plan || 'FREE',
                historyDaysLimit: user.subscription?.historyDaysLimit || 0
            },
            totalRecords: formattedDiagnoses.length
        });

    } catch (error) {
        console.error('Error fetching diagnosis history:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

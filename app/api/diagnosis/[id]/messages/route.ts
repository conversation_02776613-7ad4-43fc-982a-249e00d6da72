import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/utils/auth';
import { prisma } from '@/utils/prisma';

interface RouteParams {
    params: Promise<{ id: string }>;
}

// Add a message to a diagnosis session
export async function POST(request: NextRequest, { params }: RouteParams) {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const { id: diagnosisId } = await params;
        const body = await request.json();
        const { role, content, imageUrls = [], modelUsed, processingTime } = body;

        // Get user
        const user = await prisma.user.findUnique({
            where: { email: session.user.email }
        });

        if (!user) {
            return NextResponse.json(
                { error: 'User not found' },
                { status: 404 }
            );
        }

        // Verify diagnosis belongs to user
        const diagnosis = await prisma.medicalDiagnosis.findFirst({
            where: {
                id: diagnosisId,
                userId: user.id
            }
        });

        if (!diagnosis) {
            return NextResponse.json(
                { error: 'Diagnosis not found' },
                { status: 404 }
            );
        }

        // Create message
        const message = await prisma.diagnosisMessage.create({
            data: {
                diagnosisId,
                role: role.toUpperCase(),
                content,
                imageUrls: imageUrls || [],
                modelUsed: modelUsed || null,
                processingTime: processingTime || null
            }
        });

        // Update diagnosis status if this is an assistant message
        if (role.toLowerCase() === 'assistant') {
            await prisma.medicalDiagnosis.update({
                where: { id: diagnosisId },
                data: { 
                    status: 'COMPLETED',
                    aiResponse: content,
                    modelUsed: modelUsed || null,
                    processingTime: processingTime || null
                }
            });
        }

        // Update usage stats
        await updateUsageStats(user.id, role.toLowerCase(), imageUrls?.length || 0);

        return NextResponse.json({
            id: message.id,
            role: message.role.toLowerCase(),
            content: message.content,
            imageUrls: message.imageUrls,
            timestamp: message.createdAt
        });

    } catch (error) {
        console.error('Error adding message:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// Get messages for a diagnosis session
export async function GET(request: NextRequest, { params }: RouteParams) {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const { id: diagnosisId } = await params;

        // Get user
        const user = await prisma.user.findUnique({
            where: { email: session.user.email }
        });

        if (!user) {
            return NextResponse.json(
                { error: 'User not found' },
                { status: 404 }
            );
        }

        // Get diagnosis with messages
        const diagnosis = await prisma.medicalDiagnosis.findFirst({
            where: {
                id: diagnosisId,
                userId: user.id
            },
            include: {
                messages: {
                    orderBy: { createdAt: 'asc' }
                }
            }
        });

        if (!diagnosis) {
            return NextResponse.json(
                { error: 'Diagnosis not found' },
                { status: 404 }
            );
        }

        const messages = diagnosis.messages.map(msg => ({
            id: msg.id,
            type: msg.role.toLowerCase(),
            content: msg.content,
            imageUrls: msg.imageUrls,
            timestamp: msg.createdAt
        }));

        return NextResponse.json({
            diagnosis: {
                id: diagnosis.id,
                title: diagnosis.title,
                status: diagnosis.status,
                type: diagnosis.type,
                createdAt: diagnosis.createdAt
            },
            messages
        });

    } catch (error) {
        console.error('Error fetching messages:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

async function updateUsageStats(userId: string, role: string, imageCount: number) {
    try {
        const today = new Date();
        const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);

        // Get or create usage stats
        let usageStats = await prisma.usageStats.findUnique({
            where: { userId }
        });

        if (!usageStats) {
            usageStats = await prisma.usageStats.create({
                data: { userId }
            });
        }

        // Reset daily stats if new day
        const lastUploadDate = new Date(usageStats.dailyUploadDate);
        if (today.toDateString() !== lastUploadDate.toDateString()) {
            await prisma.usageStats.update({
                where: { id: usageStats.id },
                data: {
                    dailyUploads: 0,
                    dailyUploadDate: today
                }
            });
        }

        // Reset monthly stats if new month
        const lastMonthlyDate = new Date(usageStats.monthlyUploadDate);
        if (thisMonth.getTime() !== new Date(lastMonthlyDate.getFullYear(), lastMonthlyDate.getMonth(), 1).getTime()) {
            await prisma.usageStats.update({
                where: { id: usageStats.id },
                data: {
                    monthlyUploads: 0,
                    monthlyUploadDate: thisMonth
                }
            });
        }

        // Update stats based on role
        const updateData: any = {
            totalMessages: { increment: 1 }
        };

        if (role === 'user') {
            if (imageCount > 0) {
                updateData.dailyUploads = { increment: imageCount };
                updateData.monthlyUploads = { increment: imageCount };
                updateData.totalImages = { increment: imageCount };
            }
        } else if (role === 'assistant') {
            updateData.totalDiagnoses = { increment: 1 };
        }

        await prisma.usageStats.update({
            where: { id: usageStats.id },
            data: updateData
        });

    } catch (error) {
        console.error('Error updating usage stats:', error);
    }
}

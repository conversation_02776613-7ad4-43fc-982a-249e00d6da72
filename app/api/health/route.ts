import { NextRequest, NextResponse } from 'next/server';
// import { notifyFeishu } from '@/utils/notifyFeishu';

export async function GET(request: NextRequest) {
    try {
        // 获取请求信息
        const userAgent = request.headers.get('user-agent') || 'Unknown';
        const ip = request.headers.get('x-forwarded-for') ||
            request.headers.get('x-real-ip') ||
            'Unknown';
        const referer = request.headers.get('referer') || 'Direct';
        const timestamp = new Date().toISOString();

        // 构建健康检查响应
        const healthData = {
            status: 'healthy',
            timestamp,
            uptime: process.uptime(),
            version: process.env.npm_package_version || '1.0.0',
            environment: process.env.NODE_ENV,
        };

        // 移除飞书通知逻辑
        // const notificationPromise = (async () => {
        //     try {
        //         const message = `
        // 🔍 Health Check Access
        // Time: ${timestamp}
        // Endpoint: /api/health
        // IP: ${ip}
        // User-Agent: ${userAgent}
        // Referer: ${referer}
        // Status: Healthy
        // Uptime: ${Math.floor(process.uptime())} seconds
        // Environment: ${process.env.NODE_ENV}
        //         `.trim();

        //         await notifyFeishu.notify(message);
        //     } catch (error) {
        //         console.error('Failed to send health check notification:', error);
        //     }
        // })();

        // notificationPromise.catch(() => {
        //     // 静默处理错误，不影响健康检查响应
        // });

        return NextResponse.json(healthData, { status: 200 });
    } catch (error) {
        const errorData = {
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            error: error instanceof Error ? error.message : 'Unknown error',
            environment: process.env.NODE_ENV,
        };

        return NextResponse.json(errorData, { status: 500 });
    }
} 
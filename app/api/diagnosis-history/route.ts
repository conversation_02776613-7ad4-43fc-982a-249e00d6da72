import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/utils/auth';

interface DiagnosisRecord {
    id: string;
    title: string;
    timestamp: Date;
    type: 'TEXT_ONLY' | 'IMAGE_ONLY' | 'MULTIMODAL';
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
    messages: Array<{
        id: string;
        type: 'user' | 'assistant';
        content: string;
        image?: string;
        timestamp: Date;
    }>;
}

// 模拟用户订阅数据
const getUserSubscription = (userId: string) => {
    // 这里应该从数据库获取真实的用户订阅信息
    return {
        plan: 'FREE', // FREE, STANDARD, PRO, CLINICAL_PLUS
        historyDaysLimit: 7, // 免费版只能查看7天内的记录
        isActive: true
    };
};

// 模拟诊断历史数据
const getMockDiagnosisHistory = (userId: string, daysLimit: number): DiagnosisRecord[] => {
    const now = new Date();
    const cutoffDate = new Date(now.getTime() - (daysLimit * 24 * 60 * 60 * 1000));
    
    const allRecords: DiagnosisRecord[] = [
        {
            id: '1',
            title: 'Chest X-ray Analysis',
            timestamp: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
            type: 'IMAGE_ONLY',
            riskLevel: 'LOW',
            messages: [
                {
                    id: '1-1',
                    type: 'user',
                    content: 'Please analyze this chest X-ray image.',
                    image: '/api/placeholder/chest-xray.jpg',
                    timestamp: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000)
                },
                {
                    id: '1-2',
                    type: 'assistant',
                    content: 'Based on the chest X-ray analysis, the lungs appear clear with no obvious signs of pneumonia, pleural effusion, or other abnormalities. The heart size appears normal.',
                    timestamp: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000 + 30000)
                }
            ]
        },
        {
            id: '2',
            title: 'Skin Condition Assessment',
            timestamp: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
            type: 'MULTIMODAL',
            riskLevel: 'MEDIUM',
            messages: [
                {
                    id: '2-1',
                    type: 'user',
                    content: 'I have a rash on my arm that has been there for a week. It\'s itchy and red.',
                    image: '/api/placeholder/skin-rash.jpg',
                    timestamp: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000)
                },
                {
                    id: '2-2',
                    type: 'assistant',
                    content: 'The image shows what appears to be contact dermatitis. I recommend applying a topical corticosteroid and avoiding potential allergens. If symptoms persist, please consult a dermatologist.',
                    timestamp: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000 + 45000)
                }
            ]
        },
        {
            id: '3',
            title: 'Headache Consultation',
            timestamp: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
            type: 'TEXT_ONLY',
            riskLevel: 'LOW',
            messages: [
                {
                    id: '3-1',
                    type: 'user',
                    content: 'I\'ve been having persistent headaches for the past few days. They seem to get worse in the afternoon.',
                    timestamp: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000)
                },
                {
                    id: '3-2',
                    type: 'assistant',
                    content: 'Based on your description, this could be tension headaches. Try to maintain regular sleep patterns, stay hydrated, and consider stress management techniques. If headaches persist or worsen, please consult a healthcare provider.',
                    timestamp: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000 + 20000)
                }
            ]
        },
        {
            id: '4',
            title: 'Blood Test Results Review',
            timestamp: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
            type: 'TEXT_ONLY',
            riskLevel: 'LOW',
            messages: [
                {
                    id: '4-1',
                    type: 'user',
                    content: 'Can you help me understand my blood test results? My cholesterol levels seem high.',
                    timestamp: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000)
                },
                {
                    id: '4-2',
                    type: 'assistant',
                    content: 'Elevated cholesterol levels can be managed through dietary changes and exercise. Consider reducing saturated fats and increasing fiber intake. Please discuss these results with your doctor for personalized treatment options.',
                    timestamp: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000 + 35000)
                }
            ]
        }
    ];

    // 根据用户的历史记录限制过滤数据
    return allRecords.filter(record => record.timestamp >= cutoffDate);
};

export async function GET(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        const userId = session.user.email; // 使用email作为用户ID
        const subscription = getUserSubscription(userId);
        
        if (!subscription.isActive) {
            return NextResponse.json(
                { error: 'Subscription not active' },
                { status: 403 }
            );
        }

        const historyRecords = getMockDiagnosisHistory(userId, subscription.historyDaysLimit);
        
        return NextResponse.json({
            records: historyRecords,
            subscription: {
                plan: subscription.plan,
                historyDaysLimit: subscription.historyDaysLimit
            },
            totalRecords: historyRecords.length
        });

    } catch (error) {
        console.error('Error fetching diagnosis history:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

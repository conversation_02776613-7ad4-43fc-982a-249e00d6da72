import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { prisma } from '@/lib/prisma';
import <PERSON><PERSON> from 'stripe';

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

export async function POST(request: NextRequest) {
  if (!webhookSecret) {
    console.error('STRIPE_WEBHOOK_SECRET is not set');
    return NextResponse.json(
      { error: 'Webhook secret not configured' },
      { status: 500 }
    );
  }

  const body = await request.text();
  const signature = request.headers.get('stripe-signature');

  if (!signature) {
    return NextResponse.json(
      { error: 'No signature provided' },
      { status: 400 }
    );
  }

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
  } catch (error) {
    console.error('Webhook signature verification failed:', error);
    return NextResponse.json(
      { error: 'Invalid signature' },
      { status: 400 }
    );
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session);
        break;
      
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object as Stripe.Subscription);
        break;
      
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
        break;
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
        break;
      
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;
      
      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object as Stripe.Invoice);
        break;
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  console.log('Checkout session completed:', session.id);
  
  if (session.mode === 'subscription' && session.subscription) {
    const subscription = await stripe.subscriptions.retrieve(session.subscription as string);
    await updateUserSubscription(session.customer as string, subscription);
  }
}

async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  console.log('Subscription created:', subscription.id);
  await updateUserSubscription(subscription.customer as string, subscription);
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  console.log('Subscription updated:', subscription.id);
  await updateUserSubscription(subscription.customer as string, subscription);
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  console.log('Subscription deleted:', subscription.id);
  
  try {
    const customer = await stripe.customers.retrieve(subscription.customer as string);
    if (customer.deleted) return;

    const user = await prisma.user.findFirst({
      where: { email: (customer as Stripe.Customer).email || undefined }
    });

    if (user) {
      await prisma.user.update({
        where: { id: user.id },
        data: {
          subscriptionStatus: 'canceled',
          subscriptionId: null,
          currentPlan: 'free',
        }
      });
    }
  } catch (error) {
    console.error('Error handling subscription deletion:', error);
  }
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  console.log('Invoice payment succeeded:', invoice.id);
  
  if (invoice.subscription) {
    const subscription = await stripe.subscriptions.retrieve(invoice.subscription as string);
    await updateUserSubscription(invoice.customer as string, subscription);
  }
}

async function handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
  console.log('Invoice payment failed:', invoice.id);
  
  try {
    const customer = await stripe.customers.retrieve(invoice.customer as string);
    if (customer.deleted) return;

    const user = await prisma.user.findFirst({
      where: { email: (customer as Stripe.Customer).email || undefined }
    });

    if (user) {
      await prisma.user.update({
        where: { id: user.id },
        data: {
          subscriptionStatus: 'past_due',
        }
      });
    }
  } catch (error) {
    console.error('Error handling invoice payment failure:', error);
  }
}

async function updateUserSubscription(customerId: string, subscription: Stripe.Subscription) {
  try {
    const customer = await stripe.customers.retrieve(customerId);
    if (customer.deleted) return;

    const user = await prisma.user.findFirst({
      where: { email: (customer as Stripe.Customer).email || undefined }
    });

    if (!user) {
      console.error('User not found for customer:', customerId);
      return;
    }

    // 获取价格信息来确定计划类型
    const priceId = subscription.items.data[0]?.price.id;
    let planType = 'free';
    
    if (priceId === process.env.STRIPE_PRICE_STANDARD_MONTHLY || 
        priceId === process.env.STRIPE_PRICE_STANDARD_YEARLY) {
      planType = 'standard';
    } else if (priceId === process.env.STRIPE_PRICE_PRO_MONTHLY || 
               priceId === process.env.STRIPE_PRICE_PRO_YEARLY) {
      planType = 'pro';
    }

    await prisma.user.update({
      where: { id: user.id },
      data: {
        subscriptionId: subscription.id,
        subscriptionStatus: subscription.status,
        currentPlan: planType,
        subscriptionCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
      }
    });

    console.log(`Updated user ${user.id} subscription to ${planType}`);
  } catch (error) {
    console.error('Error updating user subscription:', error);
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/utils/auth';
import { prisma } from '@/utils/prisma';

export async function GET(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json(
                { error: 'Unauthorized' },
                { status: 401 }
            );
        }

        // Get user with subscription and usage stats
        const user = await prisma.user.findUnique({
            where: { email: session.user.email },
            include: {
                subscription: true,
                usageStats: true,
                diagnoses: {
                    orderBy: { createdAt: 'desc' },
                    take: 5,
                    include: {
                        messages: {
                            orderBy: { createdAt: 'asc' },
                            take: 1
                        }
                    }
                }
            }
        });

        if (!user) {
            return NextResponse.json(
                { error: 'User not found' },
                { status: 404 }
            );
        }

        // Create default subscription if none exists
        let subscription = user.subscription;
        if (!subscription) {
            subscription = await prisma.userSubscription.create({
                data: {
                    userId: user.id,
                    plan: 'FREE',
                    status: 'ACTIVE',
                    dailyUploadLimit: 5,
                    chatRoundsLimit: 0,
                    historyDaysLimit: 0,
                    hasStructuredFindings: false,
                    hasPdfExport: false,
                    hasImageAnalysis: false,
                    hasTeachingTools: false,
                    hasBatchUpload: false
                }
            });
        }

        // Create default usage stats if none exists
        let usageStats = user.usageStats;
        if (!usageStats) {
            usageStats = await prisma.usageStats.create({
                data: {
                    userId: user.id,
                    dailyUploads: 0,
                    monthlyUploads: 0,
                    totalDiagnoses: 0,
                    totalMessages: 0,
                    totalImages: 0,
                    pdfExports: 0,
                    teachingExports: 0
                }
            });
        }

        // Reset daily uploads if it's a new day
        const today = new Date();
        const lastUploadDate = new Date(usageStats.dailyUploadDate);
        if (today.toDateString() !== lastUploadDate.toDateString()) {
            usageStats = await prisma.usageStats.update({
                where: { id: usageStats.id },
                data: {
                    dailyUploads: 0,
                    dailyUploadDate: today
                }
            });
        }

        // Reset monthly uploads if it's a new month
        const thisMonth = today.getMonth();
        const lastUploadMonth = lastUploadDate.getMonth();
        if (thisMonth !== lastUploadMonth) {
            usageStats = await prisma.usageStats.update({
                where: { id: usageStats.id },
                data: {
                    monthlyUploads: 0,
                    monthlyUploadDate: today
                }
            });
        }

        // Format recent diagnoses
        const recentDiagnoses = user.diagnoses.map(diagnosis => ({
            id: diagnosis.id,
            title: diagnosis.title,
            timestamp: diagnosis.createdAt,
            status: diagnosis.status,
            type: diagnosis.type,
            riskLevel: diagnosis.riskLevel || 'LOW'
        }));

        // Add coming soon flags for STANDARD plan
        const subscriptionWithFlags = {
            ...subscription,
            imageAnalysisComingSoon: subscription.plan === 'STANDARD' && !subscription.hasImageAnalysis,
            teachingToolsComingSoon: subscription.plan === 'STANDARD' && !subscription.hasTeachingTools
        };

        return NextResponse.json({
            user: {
                id: user.id,
                name: user.name,
                email: user.email,
                image: user.image
            },
            subscription: subscriptionWithFlags,
            usageStats,
            recentDiagnoses
        });

    } catch (error) {
        console.error('Error fetching user profile:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

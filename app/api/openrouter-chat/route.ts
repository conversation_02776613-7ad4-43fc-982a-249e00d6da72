import { NextRequest, NextResponse } from 'next/server';

interface OpenRouterRequest {
    prompt: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
}

// 医疗AI模型列表（按优先级排序，支持故障转移）
const MEDICAL_MODELS = [
    {
        id: 'gpt-4o',
        name: 'GPT-4o (Premium)',
        model: 'openai/gpt-4o',
        description: '多模态+优秀医学推理能力，已用于真实医疗助手场景',
        free: false,
        multimodal: true
    },
    {
        id: 'gemini-2.5-flash',
        name: 'Gemini 2.5 Flash',
        model: 'google/gemini-2.5-flash',
        description: '具备医学阅读能力，较强结构化理解，支持多模态',
        free: false,
        multimodal: true
    },
    {
        id: 'mistral-small-3.2',
        name: 'Mistral Small 3.2 24B (Free)',
        model: 'mistralai/mistral-small-3.2-24b-instruct:free',
        description: '稳定、成本低，支持多模态，可适配结构化问答',
        free: true,
        multimodal: true
    },
    {
        id: 'kimi-k2',
        name: '<PERSON><PERSON> K2 (Free)',
        model: 'moonshotai/kimi-k2:free',
        description: 'MoE架构，1T参数，优秀的推理和工具使用能力',
        free: true,
        multimodal: false
    },
    {
        id: 'hunyuan-a13b',
        name: 'Hunyuan A13B (Free)',
        model: 'tencent/hunyuan-a13b-instruct:free',
        description: '腾讯MoE模型，支持推理，数学和编程能力强',
        free: true,
        multimodal: false
    },
    {
        id: 'deepseek-r1t2-chimera',
        name: 'DeepSeek R1T2 Chimera (Free)',
        model: 'tngtech/deepseek-r1t2-chimera:free',
        description: '671B参数MoE模型，强推理能力，支持长上下文',
        free: true,
        multimodal: false
    }
];



// 创建带有重试和超时的fetch函数
async function fetchWithRetry(url: string, options: RequestInit, retries = 3, timeout = 30000): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
        const response = await fetch(url, {
            ...options,
            signal: controller.signal,
            // 添加额外的headers来解决SSL问题
            headers: {
                ...options.headers,
                'User-Agent': 'MedGemma/1.0',
                'Accept': 'application/json',
                'Cache-Control': 'no-cache'
            }
        });
        clearTimeout(timeoutId);
        return response;
    } catch (error) {
        clearTimeout(timeoutId);
        if (retries > 0 && (error as Error).name !== 'AbortError') {
            console.log(`Fetch failed, retrying... (${retries} attempts left)`);
            await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒后重试
            return fetchWithRetry(url, options, retries - 1, timeout);
        }
        throw error;
    }
}

// 尝试调用模型的函数
async function tryModel(modelConfig: any, requestData: any, openrouterApiKey: string, openrouterApiUrl: string) {
    const openrouterRequest = {
        ...requestData,
        model: modelConfig.model
    };

    console.log(`Trying model: ${modelConfig.name} (${modelConfig.model})`);

    const response = await fetchWithRetry(`${openrouterApiUrl}/chat/completions`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${openrouterApiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
            'X-Title': 'MedGemma Medical Chat'
        },
        body: JSON.stringify(openrouterRequest)
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Model ${modelConfig.name} failed: ${response.status} - ${errorText}`);
    }

    return response.json();
}

export async function POST(request: NextRequest) {
    try {
        // 检查请求类型 - 支持JSON和FormData
        const contentType = request.headers.get('content-type');
        let prompt: string;
        let model: string = 'mistral-small-3.2'; // 默认使用免费的Mistral Small 3.2
        let imageFile: File | null = null;
        let imageBase64: string | null = null;

        if (contentType?.includes('multipart/form-data')) {
            // 处理FormData (支持图片上传)
            const formData = await request.formData();
            prompt = formData.get('prompt') as string;
            model = (formData.get('model') as string) || 'mistral-small-3.2';
            imageFile = formData.get('image') as File;

            // 如果有图片，转换为base64
            if (imageFile && imageFile.size > 0) {
                const bytes = await imageFile.arrayBuffer();
                const buffer = Buffer.from(bytes);
                imageBase64 = buffer.toString('base64');
            }
        } else {
            // 处理JSON请求 (向后兼容)
            const body: OpenRouterRequest = await request.json();
            prompt = body.prompt;
            model = body.model || 'mistral-small-3.2';
        }

        // 验证必需字段
        if (!prompt) {
            return NextResponse.json(
                { error: 'Prompt is required' },
                { status: 400 }
            );
        }

        // 获取请求的模型配置，如果不存在则使用默认的免费模型
        let selectedModel = MEDICAL_MODELS.find(m => m.id === model);
        if (!selectedModel) {
            selectedModel = MEDICAL_MODELS.find(m => m.id === 'mistral-small-3.2') || MEDICAL_MODELS.find(m => m.free);
        }

        // 获取OpenRouter配置
        const openrouterApiKey = process.env.OPENROUTER_API_KEY;
        const openrouterApiUrl = process.env.OPENROUTER_API_URL || 'https://openrouter.ai/api/v1';

        if (!openrouterApiKey) {
            return NextResponse.json(
                { error: 'OpenRouter API key not configured' },
                { status: 500 }
            );
        }

        // 构建消息内容
        let messageContent: any[] = [
            {
                type: "text",
                text: `You are a medical AI assistant. Please provide accurate, evidence-based medical information. Remember that this is for educational and research purposes only and should not replace professional medical advice.

${imageBase64 ? 'Please analyze the provided medical image along with the following question:' : 'Question:'} ${prompt}`
            }
        ];

        // 如果有图片且模型支持多模态，添加到消息中
        if (imageBase64 && imageFile && selectedModel?.multimodal) {
            messageContent.push({
                type: "image_url",
                image_url: {
                    url: `data:${imageFile.type};base64,${imageBase64}`
                }
            });
        } else if (imageBase64 && !selectedModel?.multimodal) {
            // 如果模型不支持多模态，在文本中说明有图片但无法处理
            messageContent[0].text += '\n\nNote: An image was provided but this model does not support image analysis. Please describe the image in text for better assistance.';
        }

        // 准备基础请求数据
        const baseRequestData = {
            messages: [
                {
                    role: "user",
                    content: messageContent
                }
            ],
            max_tokens: 1024,
            temperature: 0.7,
            top_p: 0.9
        };

        // 实现故障转移机制：优先尝试选定的模型，失败后尝试其他免费模型
        const modelsToTry = selectedModel ? [selectedModel] : [];

        // 如果选定的模型失败，添加其他免费模型作为备选
        const fallbackModels = MEDICAL_MODELS.filter(m =>
            m.free && m.id !== selectedModel?.id
        );
        modelsToTry.push(...fallbackModels);

        // 确保至少有一个模型可以尝试
        if (modelsToTry.length === 0) {
            return NextResponse.json(
                { error: 'No available models found' },
                { status: 500 }
            );
        }

        let lastError: Error | null = null;
        let successfulModel: any = null;
        let responseData: any = null;

        // 逐个尝试模型
        for (const modelConfig of modelsToTry) {
            try {
                console.log(`Attempting to use model: ${modelConfig.name}`);
                responseData = await tryModel(modelConfig, baseRequestData, openrouterApiKey, openrouterApiUrl);
                successfulModel = modelConfig;
                break; // 成功则跳出循环
            } catch (error) {
                console.error(`Model ${modelConfig.name} failed:`, error);
                lastError = error as Error;

                // 如果是最后一个模型也失败了，记录错误
                if (modelConfig === modelsToTry[modelsToTry.length - 1]) {
                    console.error('All models failed, returning error');
                }
            }
        }

        // 如果所有模型都失败了
        if (!responseData || !successfulModel) {
            console.error('All models failed. Last error:', lastError);
            return NextResponse.json(
                {
                    error: 'All available models failed to respond. Please try again later.',
                    details: lastError?.message || 'Unknown error'
                },
                { status: 500 }
            );
        }

        // 提取回复内容
        const assistantResponse = responseData.choices?.[0]?.message?.content || 'Sorry, I could not generate a response.';

        return NextResponse.json({
            response: assistantResponse,
            model: successfulModel.id,
            modelName: successfulModel.name,
            usage: responseData.usage,
            fallbackUsed: successfulModel.id !== selectedModel?.id
        });

    } catch (error) {
        console.error('Error in OpenRouter chat:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

export async function GET() {
    return NextResponse.json({
        message: 'OpenRouter Medical Chat API',
        availableModels: MEDICAL_MODELS.map(model => ({
            id: model.id,
            name: model.name,
            description: model.description,
            free: model.free,
            multimodal: model.multimodal
        })),
        freeModels: MEDICAL_MODELS.filter(m => m.free).map(m => m.id),
        defaultModel: 'mistral-small-3.2'
    });
}

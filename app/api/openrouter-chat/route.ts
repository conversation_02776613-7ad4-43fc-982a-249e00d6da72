import { NextRequest, NextResponse } from 'next/server';

interface OpenRouterRequest {
    prompt: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
}

// OpenRouter免费模型列表（支持医疗应用的模型）
const FREE_MODELS = {
    'llama-3.3-70b': 'meta-llama/llama-3.3-70b-instruct:free',
    'llama-3.1-8b': 'meta-llama/llama-3.1-8b-instruct:free', 
    'phi-4': 'microsoft/phi-4-reasoning:free',
    'hermes-3': 'nousresearch/hermes-3-llama-3.1-405b:free',
    'qwen-2.5-7b': 'qwen/qwen-2.5-7b-instruct:free'
};

export async function POST(request: NextRequest) {
    try {
        // 检查请求类型 - 支持JSON和FormData
        const contentType = request.headers.get('content-type');
        let prompt: string;
        let model: string = 'llama-3.3-70b'; // 默认使用Llama 3.3 70B
        let imageFile: File | null = null;
        let imageBase64: string | null = null;

        if (contentType?.includes('multipart/form-data')) {
            // 处理FormData (支持图片上传)
            const formData = await request.formData();
            prompt = formData.get('prompt') as string;
            model = (formData.get('model') as string) || 'llama-3.3-70b';
            imageFile = formData.get('image') as File;

            // 如果有图片，转换为base64
            if (imageFile && imageFile.size > 0) {
                const bytes = await imageFile.arrayBuffer();
                const buffer = Buffer.from(bytes);
                imageBase64 = buffer.toString('base64');
            }
        } else {
            // 处理JSON请求 (向后兼容)
            const body: OpenRouterRequest = await request.json();
            prompt = body.prompt;
            model = body.model || 'llama-3.3-70b';
        }

        // 验证必需字段
        if (!prompt) {
            return NextResponse.json(
                { error: 'Prompt is required' },
                { status: 400 }
            );
        }

        // 验证模型是否在免费列表中
        if (!FREE_MODELS[model as keyof typeof FREE_MODELS]) {
            return NextResponse.json(
                { error: 'Invalid or non-free model selected' },
                { status: 400 }
            );
        }

        // 获取OpenRouter配置
        const openrouterApiKey = process.env.OPENROUTER_API_KEY;
        const openrouterApiUrl = process.env.OPENROUTER_API_URL || 'https://openrouter.ai/api/v1';

        if (!openrouterApiKey) {
            return NextResponse.json(
                { error: 'OpenRouter API key not configured' },
                { status: 500 }
            );
        }

        // 构建消息内容
        let messageContent: any[] = [
            {
                type: "text",
                text: `You are a medical AI assistant. Please provide accurate, evidence-based medical information. Remember that this is for educational and research purposes only and should not replace professional medical advice.

${imageBase64 ? 'Please analyze the provided medical image along with the following question:' : 'Question:'} ${prompt}`
            }
        ];

        // 如果有图片，添加到消息中
        if (imageBase64 && imageFile) {
            messageContent.push({
                type: "image_url",
                image_url: {
                    url: `data:${imageFile.type};base64,${imageBase64}`
                }
            });
        }

        // 准备OpenRouter请求
        const openrouterRequest = {
            model: FREE_MODELS[model as keyof typeof FREE_MODELS],
            messages: [
                {
                    role: "user",
                    content: messageContent
                }
            ],
            max_tokens: 1024,
            temperature: 0.7,
            top_p: 0.9
        };

        // 调用OpenRouter API
        const response = await fetch(`${openrouterApiUrl}/chat/completions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${openrouterApiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
                'X-Title': 'MedGemma Medical Chat'
            },
            body: JSON.stringify(openrouterRequest)
        });

        if (!response.ok) {
            const errorData = await response.text();
            console.error('OpenRouter API error:', errorData);
            return NextResponse.json(
                { error: 'Failed to get response from OpenRouter' },
                { status: response.status }
            );
        }

        const data = await response.json();
        
        // 提取回复内容
        const assistantResponse = data.choices?.[0]?.message?.content || 'Sorry, I could not generate a response.';

        return NextResponse.json({
            response: assistantResponse,
            model: model,
            usage: data.usage
        });

    } catch (error) {
        console.error('Error in OpenRouter chat:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

export async function GET() {
    return NextResponse.json({
        message: 'OpenRouter Medical Chat API',
        availableModels: Object.keys(FREE_MODELS),
        defaultModel: 'llama-3.3-70b'
    });
}

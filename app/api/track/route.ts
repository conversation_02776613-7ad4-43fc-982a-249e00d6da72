import { NextRequest, NextResponse } from 'next/server';
// import { notifyFeishu } from '@/utils/notifyFeishu';

interface TrackingData {
    event: string;
    category: string;
    label: string;
    value?: number;
    page?: string;
    userId?: string;
    sessionId?: string;
    additionalData?: Record<string, any>;
}

export async function POST(request: NextRequest) {
    try {
        const body: TrackingData = await request.json();

        // 获取请求信息
        const userAgent = request.headers.get('user-agent') || 'Unknown';
        const ip = request.headers.get('x-forwarded-for') ||
            request.headers.get('x-real-ip') ||
            'Unknown';
        const referer = request.headers.get('referer') || 'Direct';
        const timestamp = new Date().toISOString();

        // 验证必需字段
        if (!body.event || !body.category || !body.label) {
            return NextResponse.json(
                { error: 'Missing required fields: event, category, label' },
                { status: 400 }
            );
        }

        // 移除飞书通知逻辑
        // const notificationPromise = (async () => {
        //     try {
        //         let message = `
        // 🎯 User Behavior Tracking
        // Event: ${body.event}
        // Category: ${body.category}
        // Label: ${body.label}
        // Value: ${body.value || 0}
        // Time: ${timestamp}
        // Page: ${body.page || 'Unknown'}
        // IP: ${ip}
        // User-Agent: ${userAgent}
        // Referer: ${referer}
        //         `.trim();

        //         // 添加用户和会话信息（如果有）
        //         if (body.userId) {
        //             message += `\nUser ID: ${body.userId}`;
        //         }
        //         if (body.sessionId) {
        //             message += `\nSession ID: ${body.sessionId}`;
        //         }

        //         // 添加额外数据（如果有）
        //         if (body.additionalData && Object.keys(body.additionalData).length > 0) {
        //             message += `\nAdditional Data: ${JSON.stringify(body.additionalData)}`;
        //         }

        //         await notifyFeishu.notify(message);
        //     } catch (error) {
        //         console.error('Failed to send tracking notification:', error);
        //     }
        // })();

        // notificationPromise.catch(() => {
        //     // 静默处理错误
        // });

        return NextResponse.json({
            success: true,
            timestamp,
            message: 'Event tracked successfully'
        });

    } catch (error) {
        console.error('Tracking API error:', error);
        return NextResponse.json(
            { error: 'Failed to track event' },
            { status: 500 }
        );
    }
}

export async function GET(request: NextRequest) {
    // 处理 GET 请求的简单事件跟踪
    const url = new URL(request.url);
    const event = url.searchParams.get('event');
    const category = url.searchParams.get('category');
    const label = url.searchParams.get('label');
    const value = url.searchParams.get('value');
    const page = url.searchParams.get('page');

    if (!event || !category || !label) {
        return NextResponse.json(
            { error: 'Missing required parameters: event, category, label' },
            { status: 400 }
        );
    }

    // 获取请求信息
    const userAgent = request.headers.get('user-agent') || 'Unknown';
    const ip = request.headers.get('x-forwarded-for') ||
        request.headers.get('x-real-ip') ||
        'Unknown';
    const referer = request.headers.get('referer') || 'Direct';
    const timestamp = new Date().toISOString();

    // 移除飞书通知逻辑
    // const notificationPromise = (async () => {
    //     try {
    //         const message = `
    // 🎯 User Behavior Tracking (GET)
    // Event: ${event}
    // Category: ${category}
    // Label: ${label}
    // Value: ${value || 0}
    // Time: ${timestamp}
    // Page: ${page || 'Unknown'}
    // IP: ${ip}
    // User-Agent: ${userAgent}
    // Referer: ${referer}
    //     `.trim();

    //         await notifyFeishu.notify(message);
    //     } catch (error) {
    //         console.error('Failed to send tracking notification:', error);
    //     }
    // })();

    // notificationPromise.catch(() => {
    //     // 静默处理错误
    // });

    return NextResponse.json({
        success: true,
        timestamp,
        message: 'Event tracked successfully'
    });
} 
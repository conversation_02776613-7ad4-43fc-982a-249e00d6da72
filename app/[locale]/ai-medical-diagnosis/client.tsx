'use client';

import { useState, useRef, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useSession } from 'next-auth/react';
import MedGemmaHeader from '@/components/MedGemmaHeader';
import ChatMessage from '@/components/ChatMessage';
import ImageUpload from '@/components/ImageUpload';
import Footer from '@/components/ui/Footer';

interface ChatMessageData {
    id: string;
    type: 'user' | 'assistant';
    content: string;
    image?: string;
    timestamp: Date;
    isTyping?: boolean;
}

interface MedGeminiResponse {
    response: string;
    model: string;
    usage: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}

interface DiagnosisHistory {
    id: string;
    title: string;
    timestamp: Date;
    messages: ChatMessageData[];
}

export default function AIMedicalDiagnosisClient() {
    const t = useTranslations('pages.aiMedicalDiagnosis');
    const { data: session } = useSession();
    const [messages, setMessages] = useState<ChatMessageData[]>([]);
    const [inputText, setInputText] = useState('');
    const [selectedImage, setSelectedImage] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [model, setModel] = useState<'gpt-4o' | 'gemini-2.5-flash' | 'mistral-small-3.2' | 'kimi-k2' | 'hunyuan-a13b' | 'deepseek-r1t2-chimera'>('mistral-small-3.2');
    const [showHistory, setShowHistory] = useState(false);
    const [diagnosisHistory, setDiagnosisHistory] = useState<DiagnosisHistory[]>([]);

    // 用户状态基于实际登录状态
    const userStatus = {
        isLoggedIn: !!session,
        isPremium: false, // 这里可以根据实际的付费状态来设置
    };

    const messagesEndRef = useRef<HTMLDivElement>(null);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    // 加载历史诊断记录
    useEffect(() => {
        const loadDiagnosisHistory = async () => {
            if (!session) {
                setDiagnosisHistory([]);
                return;
            }

            try {
                const response = await fetch('/api/diagnosis-history');
                if (response.ok) {
                    const data = await response.json();
                    const historyRecords: DiagnosisHistory[] = data.records.map((record: any) => ({
                        id: record.id,
                        title: record.title,
                        timestamp: new Date(record.timestamp),
                        messages: record.messages.map((msg: any) => ({
                            ...msg,
                            timestamp: new Date(msg.timestamp)
                        }))
                    }));
                    setDiagnosisHistory(historyRecords);
                } else {
                    console.error('Failed to load diagnosis history');
                    setDiagnosisHistory([]);
                }
            } catch (error) {
                console.error('Error loading diagnosis history:', error);
                setDiagnosisHistory([]);
            }
        };

        loadDiagnosisHistory();
    }, [session]);

    const handleImageSelect = (file: File, preview: string) => {
        setSelectedImage(file);
        setImagePreview(preview);
    };

    const handleImageRemove = () => {
        setSelectedImage(null);
        setImagePreview(null);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!inputText.trim() && !selectedImage) return;
        if (isLoading) return;

        const userMessage: ChatMessageData = {
            id: Date.now().toString(),
            type: 'user',
            content: inputText,
            image: imagePreview || undefined,
            timestamp: new Date()
        };

        setMessages(prev => [...prev, userMessage]);
        setInputText('');
        setImagePreview(null);
        setSelectedImage(null);
        setIsLoading(true);

        // 添加打字指示器
        const typingMessage: ChatMessageData = {
            id: 'typing',
            type: 'assistant',
            content: '',
            timestamp: new Date(),
            isTyping: true
        };
        setMessages(prev => [...prev, typingMessage]);

        try {
            const formData = new FormData();
            formData.append('message', inputText);
            formData.append('model', model);
            if (selectedImage) {
                formData.append('image', selectedImage);
            }

            const response = await fetch('/api/openrouter-chat', {
                method: 'POST',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: MedGeminiResponse = await response.json();

            // 移除打字指示器并添加实际回复
            setMessages(prev => {
                const filtered = prev.filter(msg => msg.id !== 'typing');
                return [...filtered, {
                    id: Date.now().toString(),
                    type: 'assistant',
                    content: data.response,
                    timestamp: new Date()
                }];
            });

        } catch (error) {
            console.error('Error calling API:', error);
            
            // 移除打字指示器并添加错误消息
            setMessages(prev => {
                const filtered = prev.filter(msg => msg.id !== 'typing');
                return [...filtered, {
                    id: Date.now().toString(),
                    type: 'assistant',
                    content: t('content.error.apiError'),
                    timestamp: new Date()
                }];
            });
        } finally {
            setIsLoading(false);
        }
    };

    const loadHistoryDiagnosis = (diagnosis: DiagnosisHistory) => {
        setMessages(diagnosis.messages);
        setShowHistory(false);
    };

    const startNewDiagnosis = () => {
        setMessages([]);
        setShowHistory(false);
    };

    return (
        <div className="min-h-screen bg-gray-50">
            <MedGemmaHeader />
            
            {/* Hero Section */}
            <section className="pt-28 sm:pt-32 md:pt-36 pb-4 sm:pb-6 md:pb-8 bg-gradient-to-b from-blue-100 to-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <div className="inline-flex items-center px-3 py-1.5 sm:px-4 sm:py-2 bg-blue-100 text-blue-800 rounded-full text-xs sm:text-sm font-medium mb-3 sm:mb-4">
                        <svg className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {t('content.hero.badge')}
                    </div>
                    <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-blue-800 mb-3 sm:mb-4">
                        {t('content.hero.title')}
                    </h1>
                    <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-blue-700 max-w-4xl mx-auto px-2">
                        {t('content.hero.description')}
                    </p>
                </div>
            </section>

            {/* Main Chat Interface */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 md:py-8">
                <div className="grid grid-cols-1 xl:grid-cols-12 gap-6 xl:gap-8">
                    {/* Sidebar for History (Desktop) */}
                    <div className="hidden xl:block xl:col-span-3">
                        <div className="sticky top-24">
                            {/* Status Card */}
                            <div className="mb-6 p-4 bg-white rounded-lg shadow-sm border border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-800 mb-4">{t('content.status.title')}</h3>
                                <div className="space-y-3">
                                    {/* Login Status */}
                                    <div className="flex items-center space-x-2">
                                        <div className={`w-2 h-2 rounded-full ${userStatus.isLoggedIn ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                                        <span className="text-sm text-gray-600">
                                            {userStatus.isLoggedIn ? t('content.status.loggedIn') : t('content.status.notLoggedIn')}
                                        </span>
                                    </div>
                                    
                                    {/* Premium Status */}
                                    <div className="flex items-center space-x-2">
                                        <div className={`w-2 h-2 rounded-full ${userStatus.isPremium ? 'bg-blue-500' : 'bg-gray-400'}`}></div>
                                        <span className="text-sm text-gray-600">
                                            {userStatus.isPremium ? t('content.status.premium') : t('content.status.free')}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="mb-6 space-y-2">
                                <button
                                    onClick={startNewDiagnosis}
                                    className="w-full px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors font-medium"
                                >
                                    {t('content.actions.newDiagnosis')}
                                </button>
                                {userStatus.isLoggedIn && (
                                    <button
                                        onClick={() => setShowHistory(!showHistory)}
                                        className="w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors font-medium"
                                    >
                                        {t('content.actions.viewHistory')}
                                    </button>
                                )}
                            </div>

                            {/* History Panel (Desktop) */}
                            {showHistory && userStatus.isLoggedIn && (
                                <div className="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-4">{t('content.history.title')}</h3>
                                    {diagnosisHistory.length > 0 ? (
                                        <div className="space-y-2">
                                            {diagnosisHistory.map((diagnosis) => (
                                                <button
                                                    key={diagnosis.id}
                                                    onClick={() => loadHistoryDiagnosis(diagnosis)}
                                                    className="w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
                                                >
                                                    <div className="font-medium text-gray-800 text-sm">{diagnosis.title}</div>
                                                    <div className="text-xs text-gray-500">
                                                        {diagnosis.timestamp.toLocaleDateString()}
                                                    </div>
                                                </button>
                                            ))}
                                        </div>
                                    ) : (
                                        <p className="text-gray-500 text-sm">{t('content.history.empty')}</p>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Main Chat Area */}
                    <div className="xl:col-span-9">
                        {/* Mobile Status Bar */}
                        <div className="xl:hidden mb-4 sm:mb-6 p-3 sm:p-4 bg-white rounded-lg shadow-sm border border-gray-200">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                                <div className="flex items-center space-x-4">
                                    {/* Login Status */}
                                    <div className="flex items-center space-x-2">
                                        <div className={`w-2 h-2 rounded-full ${userStatus.isLoggedIn ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                                        <span className="text-sm text-gray-600">
                                            {userStatus.isLoggedIn ? t('content.status.loggedIn') : t('content.status.notLoggedIn')}
                                        </span>
                                    </div>

                                    {/* Premium Status */}
                                    <div className="flex items-center space-x-2">
                                        <div className={`w-2 h-2 rounded-full ${userStatus.isPremium ? 'bg-blue-500' : 'bg-gray-400'}`}></div>
                                        <span className="text-sm text-gray-600">
                                            {userStatus.isPremium ? t('content.status.premium') : t('content.status.free')}
                                        </span>
                                    </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex items-center space-x-2">
                                    {userStatus.isLoggedIn && (
                                        <button
                                            onClick={() => setShowHistory(!showHistory)}
                                            className="px-3 py-1.5 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                                        >
                                            {t('content.actions.viewHistory')}
                                        </button>
                                    )}
                                    <button
                                        onClick={startNewDiagnosis}
                                        className="px-3 py-1.5 text-sm bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
                                    >
                                        {t('content.actions.newDiagnosis')}
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* Mobile History Panel */}
                        {showHistory && userStatus.isLoggedIn && (
                            <div className="xl:hidden mb-6 p-4 bg-white rounded-lg shadow-sm border border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-800 mb-4">{t('content.history.title')}</h3>
                                {diagnosisHistory.length > 0 ? (
                                    <div className="space-y-2">
                                        {diagnosisHistory.map((diagnosis) => (
                                            <button
                                                key={diagnosis.id}
                                                onClick={() => loadHistoryDiagnosis(diagnosis)}
                                                className="w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
                                            >
                                                <div className="font-medium text-gray-800">{diagnosis.title}</div>
                                                <div className="text-sm text-gray-500">
                                                    {diagnosis.timestamp.toLocaleDateString()}
                                                </div>
                                            </button>
                                        ))}
                                    </div>
                                ) : (
                                    <p className="text-gray-500">{t('content.history.empty')}</p>
                                )}
                            </div>
                        )}

                        {/* Chat Messages */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-4 sm:mb-6">
                            <div className="h-[400px] sm:h-[500px] lg:h-[600px] xl:h-[650px] overflow-y-auto p-4 sm:p-6 space-y-4">
                                {messages.length === 0 ? (
                                    <div className="flex flex-col items-center justify-center h-full text-center">
                                        <div className="w-16 h-16 sm:w-20 sm:h-20 bg-blue-100 rounded-full flex items-center justify-center mb-4 sm:mb-6">
                                            <svg className="w-8 h-8 sm:w-10 sm:h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                            </svg>
                                        </div>
                                        <h3 className="text-lg sm:text-xl lg:text-2xl font-semibold text-gray-800 mb-2 sm:mb-3">
                                            {t('content.chat.welcome.title')}
                                        </h3>
                                        <p className="text-gray-600 max-w-md lg:max-w-lg text-sm sm:text-base lg:text-lg">
                                            {t('content.chat.welcome.description')}
                                        </p>
                                    </div>
                                ) : (
                                    <>
                                        {messages.map((message) => (
                                            <ChatMessage
                                                key={message.id}
                                                id={message.id}
                                                type={message.type}
                                                content={message.content}
                                                image={message.image}
                                                timestamp={message.timestamp}
                                                isTyping={message.isTyping}
                                            />
                                        ))}
                                        <div ref={messagesEndRef} />
                                    </>
                                )}
                            </div>
                        </div>

                        {/* Input Form */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4 md:p-6">
                            <form onSubmit={handleSubmit}>
                                {/* Desktop Layout */}
                                <div className="hidden sm:block">
                                    <div className="flex items-center gap-2 md:gap-3 lg:gap-4">
                                        <div className="flex-1 min-w-0">
                                            <textarea
                                                value={inputText}
                                                onChange={(e) => setInputText(e.target.value)}
                                                placeholder={t('content.chat.input.placeholder')}
                                                className="w-full px-3 py-2.5 sm:px-4 sm:py-3 md:px-4 md:py-3 lg:px-5 lg:py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-sm lg:text-base resize-none h-[44px] sm:h-[48px] md:h-[56px] lg:h-[64px] max-h-32"
                                                rows={1}
                                                disabled={isLoading}
                                                onInput={(e) => {
                                                    const target = e.target as HTMLTextAreaElement;
                                                    target.style.height = 'auto';
                                                    target.style.height = Math.min(target.scrollHeight, 128) + 'px';
                                                }}
                                            />
                                        </div>
                                        <div className="flex-shrink-0">
                                            <ImageUpload
                                                onImageSelect={handleImageSelect}
                                                onImageRemove={handleImageRemove}
                                                imagePreview={imagePreview}
                                                disabled={isLoading}
                                            />
                                        </div>
                                        <div className="flex-shrink-0">
                                            <button
                                                type="submit"
                                                disabled={isLoading || (!inputText.trim() && !selectedImage)}
                                                className="h-[44px] sm:h-[48px] md:h-[56px] lg:h-[64px] px-4 md:px-6 lg:px-8 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium text-sm lg:text-base flex items-center justify-center min-w-[80px] md:min-w-[100px] lg:min-w-[120px]"
                                            >
                                                {isLoading ? (
                                                    <div className="flex items-center space-x-1.5">
                                                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                                        <span className="hidden md:inline">{t('content.chat.input.sending')}</span>
                                                    </div>
                                                ) : (
                                                    <div className="flex items-center space-x-1.5">
                                                        <svg className="w-4 h-4 lg:w-5 lg:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                                        </svg>
                                                        <span className="hidden md:inline">{t('content.chat.input.send')}</span>
                                                    </div>
                                                )}
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                {/* Mobile Layout */}
                                <div className="sm:hidden space-y-3">
                                    <textarea
                                        value={inputText}
                                        onChange={(e) => setInputText(e.target.value)}
                                        placeholder={t('content.chat.input.placeholder')}
                                        className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-sm resize-none h-[44px] max-h-32"
                                        rows={1}
                                        disabled={isLoading}
                                        onInput={(e) => {
                                            const target = e.target as HTMLTextAreaElement;
                                            target.style.height = 'auto';
                                            target.style.height = Math.min(target.scrollHeight, 128) + 'px';
                                        }}
                                    />
                                    <div className="flex items-center gap-3">
                                        <div className="flex-shrink-0">
                                            <ImageUpload
                                                onImageSelect={handleImageSelect}
                                                onImageRemove={handleImageRemove}
                                                imagePreview={imagePreview}
                                                disabled={isLoading}
                                            />
                                        </div>
                                        <button
                                            type="submit"
                                            disabled={isLoading || (!inputText.trim() && !selectedImage)}
                                            className="flex-1 h-[44px] px-4 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium text-sm flex items-center justify-center"
                                        >
                                            {isLoading ? (
                                                <div className="flex items-center space-x-2">
                                                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                                    <span>{t('content.chat.input.sending')}</span>
                                                </div>
                                            ) : (
                                                <div className="flex items-center space-x-2">
                                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                                    </svg>
                                                    <span>{t('content.chat.input.send')}</span>
                                                </div>
                                            )}
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <Footer />
        </div>
    );
}

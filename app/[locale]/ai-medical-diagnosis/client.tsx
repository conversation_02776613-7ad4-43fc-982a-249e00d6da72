'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import MedGemmaHeader from '@/components/MedGemmaHeader';
import ChatMessage from '@/components/ChatMessage';
import ImageUpload from '@/components/ImageUpload';
import Footer from '@/components/ui/Footer';

interface ChatMessageData {
    id: string;
    type: 'user' | 'assistant';
    content: string;
    image?: string;
    timestamp: Date;
    isTyping?: boolean;
}

interface OpenRouterResponse {
    response: string;
    model: string;
    modelName: string;
    usage?: {
        promptTokens?: number;
        completionTokens?: number;
        totalTokens?: number;
    };
    fallbackUsed?: boolean;
}

interface DiagnosisHistory {
    id: string;
    title: string;
    timestamp: Date;
    messages: ChatMessageData[];
}

export default function AIMedicalDiagnosisClient() {
    const t = useTranslations('pages.aiMedicalDiagnosis');
    const { data: session } = useSession();
    const [messages, setMessages] = useState<ChatMessageData[]>([]);
    const [inputText, setInputText] = useState('');
    const [selectedImage, setSelectedImage] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [model, setModel] = useState<'gpt-4o' | 'gemini-2.5-flash' | 'mistral-small-3.2' | 'kimi-k2' | 'hunyuan-a13b' | 'deepseek-r1t2-chimera'>('mistral-small-3.2');
    const [showHistory, setShowHistory] = useState(false);
    const [diagnosisHistory, setDiagnosisHistory] = useState<DiagnosisHistory[]>([]);
    const [currentDiagnosisId, setCurrentDiagnosisId] = useState<string | null>(null);
    const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null);

    // 用户状态基于实际登录状态
    const userStatus = {
        isLoggedIn: !!session,
        isPremium: false, // 这里可以根据实际的付费状态来设置
    };

    const messagesEndRef = useRef<HTMLDivElement>(null);
    const eventSourceRef = useRef<EventSource | null>(null);

    // 平滑滚动到底部
    const scrollToBottom = useCallback(() => {
        messagesEndRef.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'end'
        });
    }, []);

    // 在消息更新时滚动到底部
    useEffect(() => {
        scrollToBottom();
    }, [messages, scrollToBottom]);

    // 清理EventSource
    useEffect(() => {
        const eventSource = eventSourceRef.current;
        return () => {
            if (eventSource) {
                eventSource.close();
            }
        };
    }, []);

    // 加载历史诊断记录
    useEffect(() => {
        const loadDiagnosisHistory = async () => {
            if (!session) {
                setDiagnosisHistory([]);
                return;
            }

            try {
                const response = await fetch('/api/diagnosis');
                if (response.ok) {
                    const data = await response.json();
                    const historyRecords: DiagnosisHistory[] = data.records.map((record: any) => ({
                        id: record.id,
                        title: record.title,
                        timestamp: new Date(record.timestamp),
                        messages: record.messages.map((msg: any) => ({
                            ...msg,
                            timestamp: new Date(msg.timestamp)
                        }))
                    }));
                    setDiagnosisHistory(historyRecords);
                } else {
                    console.error('Failed to load diagnosis history');
                    setDiagnosisHistory([]);
                }
            } catch (error) {
                console.error('Error loading diagnosis history:', error);
                setDiagnosisHistory([]);
            }
        };

        loadDiagnosisHistory();
    }, [session]);

    const handleImageSelect = (file: File, preview: string) => {
        setSelectedImage(file);
        setImagePreview(preview);
    };

    const handleImageRemove = () => {
        setSelectedImage(null);
        setImagePreview(null);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!inputText.trim() && !selectedImage) return;
        if (isLoading) return;
        if (!session) return;

        const userMessage: ChatMessageData = {
            id: Date.now().toString(),
            type: 'user',
            content: inputText,
            image: imagePreview || undefined,
            timestamp: new Date()
        };

        setMessages(prev => [...prev, userMessage]);
        const originalInputText = inputText;
        const originalSelectedImage = selectedImage;
        setInputText('');
        setImagePreview(null);
        setSelectedImage(null);
        setIsLoading(true);

        // 添加打字指示器
        const typingMessage: ChatMessageData = {
            id: 'typing',
            type: 'assistant',
            content: '',
            timestamp: new Date(),
            isTyping: true
        };
        setMessages(prev => [...prev, typingMessage]);

        try {
            // Create new diagnosis session if this is the first message
            let diagnosisId = currentDiagnosisId;
            if (!diagnosisId) {
                const diagnosisType = originalSelectedImage ?
                    (originalInputText.trim() ? 'MULTIMODAL' : 'IMAGE_ONLY') :
                    'TEXT_ONLY';

                const createResponse = await fetch('/api/diagnosis', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        title: originalInputText.slice(0, 50) || 'Medical Consultation',
                        type: diagnosisType
                    })
                });

                if (createResponse.ok) {
                    const diagnosisData = await createResponse.json();
                    diagnosisId = diagnosisData.id;
                    setCurrentDiagnosisId(diagnosisId);
                } else {
                    throw new Error('Failed to create diagnosis session');
                }
            }

            // Save user message to database
            const imageUrls = originalSelectedImage ? [imagePreview || ''] : [];
            await fetch(`/api/diagnosis/${diagnosisId}/messages`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    role: 'user',
                    content: originalInputText,
                    imageUrls
                })
            });

            // 移除打字指示器并创建流式消息
            const streamingId = `streaming-${Date.now()}`;
            setStreamingMessageId(streamingId);
            setMessages(prev => {
                const filtered = prev.filter(msg => msg.id !== 'typing');
                return [...filtered, {
                    id: streamingId,
                    type: 'assistant',
                    content: '',
                    timestamp: new Date()
                }];
            });

            // Call AI API with streaming
            const formData = new FormData();
            formData.append('message', originalInputText);
            formData.append('model', model);
            if (originalSelectedImage) {
                formData.append('image', originalSelectedImage);
            }

            const response = await fetch('/api/openrouter-chat', {
                method: 'POST',
                headers: {
                    'Accept': 'text/event-stream',
                },
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // 处理流式响应
            const reader = response.body?.getReader();
            const decoder = new TextDecoder();
            let fullResponse = '';
            let usedModel = '';

            if (reader) {
                try {
                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const data = line.slice(6);
                                if (data === '[DONE]') break;

                                try {
                                    const parsed = JSON.parse(data);
                                    if (parsed.error) {
                                        throw new Error(parsed.error);
                                    }

                                    if (parsed.content) {
                                        fullResponse += parsed.content;
                                        usedModel = parsed.model || model;

                                        // 实时更新消息内容
                                        setMessages(prev => prev.map(msg =>
                                            msg.id === streamingId
                                                ? { ...msg, content: fullResponse }
                                                : msg
                                        ));
                                    }
                                } catch (e) {
                                    // 忽略解析错误
                                }
                            }
                        }
                    }
                } finally {
                    reader.releaseLock();
                }
            }

            // Save AI response to database
            if (fullResponse) {
                await fetch(`/api/diagnosis/${diagnosisId}/messages`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        role: 'assistant',
                        content: fullResponse,
                        modelUsed: usedModel
                    })
                });
            }

        } catch (error) {
            console.error('Error calling API:', error);

            // 移除流式消息并添加错误消息
            setMessages(prev => {
                const filtered = prev.filter(msg => msg.id !== streamingMessageId && msg.id !== 'typing');
                return [...filtered, {
                    id: Date.now().toString(),
                    type: 'assistant',
                    content: t('content.error.apiError'),
                    timestamp: new Date()
                }];
            });
        } finally {
            setIsLoading(false);
            setStreamingMessageId(null);
        }
    };

    const loadHistoryDiagnosis = async (diagnosis: DiagnosisHistory) => {
        try {
            // Load messages from database
            const response = await fetch(`/api/diagnosis/${diagnosis.id}/messages`);
            if (response.ok) {
                const data = await response.json();
                const messages = data.messages.map((msg: any) => ({
                    ...msg,
                    timestamp: new Date(msg.timestamp)
                }));
                setMessages(messages);
                setCurrentDiagnosisId(diagnosis.id);
            } else {
                // Fallback to cached messages
                setMessages(diagnosis.messages);
                setCurrentDiagnosisId(diagnosis.id);
            }
        } catch (error) {
            console.error('Error loading diagnosis messages:', error);
            // Fallback to cached messages
            setMessages(diagnosis.messages);
            setCurrentDiagnosisId(diagnosis.id);
        }
        setShowHistory(false);
    };

    const startNewDiagnosis = () => {
        setMessages([]);
        setCurrentDiagnosisId(null);
        setShowHistory(false);
    };

    return (
        <div className="min-h-screen bg-gray-50">
            <MedGemmaHeader />
            
            {/* Hero Section */}
            <section className="pt-28 sm:pt-32 md:pt-36 pb-4 sm:pb-6 md:pb-8 bg-gradient-to-b from-blue-100 to-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <div className="inline-flex items-center px-3 py-1.5 sm:px-4 sm:py-2 bg-blue-100 text-blue-800 rounded-full text-xs sm:text-sm font-medium mb-3 sm:mb-4">
                        <Image
                            src="/logo.png"
                            alt="MedGemma Logo"
                            width={16}
                            height={16}
                            className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2"
                        />
                        {t('content.hero.badge')}
                    </div>
                    <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-blue-800 mb-3 sm:mb-4">
                        {t('content.hero.title')}
                    </h1>
                    <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-blue-700 max-w-4xl mx-auto px-2">
                        {t('content.hero.description')}
                    </p>
                </div>
            </section>

            {/* Main Chat Interface */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 md:py-8">
                <div className="grid grid-cols-1 xl:grid-cols-12 gap-6 xl:gap-8">
                    {/* Sidebar for History (Desktop) */}
                    <div className="hidden xl:block xl:col-span-3">
                        <div className="sticky top-24">
                            {/* Status Card */}
                            <div className="mb-6 p-4 bg-white rounded-lg shadow-sm border border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-800 mb-4">{t('content.status.title')}</h3>
                                <div className="space-y-3">
                                    {/* Login Status */}
                                    <div className="flex items-center space-x-2">
                                        <div className={`w-2 h-2 rounded-full ${userStatus.isLoggedIn ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                                        <span className="text-sm text-gray-600">
                                            {userStatus.isLoggedIn ? t('content.status.loggedIn') : t('content.status.notLoggedIn')}
                                        </span>
                                    </div>
                                    
                                    {/* Premium Status */}
                                    <div className="flex items-center space-x-2">
                                        <div className={`w-2 h-2 rounded-full ${userStatus.isPremium ? 'bg-blue-500' : 'bg-gray-400'}`}></div>
                                        <span className="text-sm text-gray-600">
                                            {userStatus.isPremium ? t('content.status.premium') : t('content.status.free')}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="mb-6 space-y-2">
                                <button
                                    onClick={startNewDiagnosis}
                                    className="w-full px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors font-medium"
                                >
                                    {t('content.actions.newDiagnosis')}
                                </button>
                                {userStatus.isLoggedIn && (
                                    <button
                                        onClick={() => setShowHistory(!showHistory)}
                                        className="w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors font-medium"
                                    >
                                        {t('content.actions.viewHistory')}
                                    </button>
                                )}
                            </div>

                            {/* History Panel (Desktop) */}
                            {showHistory && userStatus.isLoggedIn && (
                                <div className="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-4">{t('content.history.title')}</h3>
                                    {diagnosisHistory.length > 0 ? (
                                        <div className="space-y-2">
                                            {diagnosisHistory.map((diagnosis) => (
                                                <button
                                                    key={diagnosis.id}
                                                    onClick={() => loadHistoryDiagnosis(diagnosis)}
                                                    className="w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
                                                >
                                                    <div className="font-medium text-gray-800 text-sm">{diagnosis.title}</div>
                                                    <div className="text-xs text-gray-500">
                                                        {diagnosis.timestamp.toLocaleDateString()}
                                                    </div>
                                                </button>
                                            ))}
                                        </div>
                                    ) : (
                                        <p className="text-gray-500 text-sm">{t('content.history.empty')}</p>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Main Chat Area */}
                    <div className="xl:col-span-9">
                        {/* Mobile Status Bar */}
                        <div className="xl:hidden mb-4 sm:mb-6 p-3 sm:p-4 bg-white rounded-lg shadow-sm border border-gray-200">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                                <div className="flex items-center space-x-4">
                                    {/* Login Status */}
                                    <div className="flex items-center space-x-2">
                                        <div className={`w-2 h-2 rounded-full ${userStatus.isLoggedIn ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                                        <span className="text-sm text-gray-600">
                                            {userStatus.isLoggedIn ? t('content.status.loggedIn') : t('content.status.notLoggedIn')}
                                        </span>
                                    </div>

                                    {/* Premium Status */}
                                    <div className="flex items-center space-x-2">
                                        <div className={`w-2 h-2 rounded-full ${userStatus.isPremium ? 'bg-blue-500' : 'bg-gray-400'}`}></div>
                                        <span className="text-sm text-gray-600">
                                            {userStatus.isPremium ? t('content.status.premium') : t('content.status.free')}
                                        </span>
                                    </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex items-center space-x-2">
                                    {userStatus.isLoggedIn && (
                                        <button
                                            onClick={() => setShowHistory(!showHistory)}
                                            className="px-3 py-1.5 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                                        >
                                            {t('content.actions.viewHistory')}
                                        </button>
                                    )}
                                    <button
                                        onClick={startNewDiagnosis}
                                        className="px-3 py-1.5 text-sm bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
                                    >
                                        {t('content.actions.newDiagnosis')}
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* Mobile History Panel */}
                        {showHistory && userStatus.isLoggedIn && (
                            <div className="xl:hidden mb-6 p-4 bg-white rounded-lg shadow-sm border border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-800 mb-4">{t('content.history.title')}</h3>
                                {diagnosisHistory.length > 0 ? (
                                    <div className="space-y-2">
                                        {diagnosisHistory.map((diagnosis) => (
                                            <button
                                                key={diagnosis.id}
                                                onClick={() => loadHistoryDiagnosis(diagnosis)}
                                                className="w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
                                            >
                                                <div className="font-medium text-gray-800">{diagnosis.title}</div>
                                                <div className="text-sm text-gray-500">
                                                    {diagnosis.timestamp.toLocaleDateString()}
                                                </div>
                                            </button>
                                        ))}
                                    </div>
                                ) : (
                                    <p className="text-gray-500">{t('content.history.empty')}</p>
                                )}
                            </div>
                        )}

                        {/* Chat Messages */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-4 sm:mb-6">
                            <div className="h-[400px] sm:h-[500px] lg:h-[600px] xl:h-[650px] overflow-y-auto p-4 sm:p-6 space-y-4">
                                {messages.length === 0 ? (
                                    <div className="flex flex-col items-center justify-center h-full text-center">
                                        <div className="w-16 h-16 sm:w-20 sm:h-20 bg-blue-100 rounded-full flex items-center justify-center mb-4 sm:mb-6">
                                            <svg className="w-8 h-8 sm:w-10 sm:h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                            </svg>
                                        </div>
                                        <h3 className="text-lg sm:text-xl lg:text-2xl font-semibold text-gray-800 mb-2 sm:mb-3">
                                            {t('content.chat.welcome.title')}
                                        </h3>
                                        <p className="text-gray-600 max-w-md lg:max-w-lg text-sm sm:text-base lg:text-lg">
                                            {t('content.chat.welcome.description')}
                                        </p>
                                    </div>
                                ) : (
                                    <>
                                        {messages.map((message) => (
                                            <ChatMessage
                                                key={message.id}
                                                id={message.id}
                                                type={message.type}
                                                content={message.content}
                                                image={message.image}
                                                timestamp={message.timestamp}
                                                isTyping={message.isTyping}
                                            />
                                        ))}
                                        <div ref={messagesEndRef} />
                                    </>
                                )}
                            </div>
                        </div>

                        {/* Input Form */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4 md:p-6">
                            <form onSubmit={handleSubmit}>
                                {/* Desktop Layout */}
                                <div className="hidden sm:block">
                                    <div className="flex items-center gap-2 md:gap-3 lg:gap-4">
                                        <div className="flex-1 min-w-0 relative">
                                            <div className="relative border border-gray-300 rounded-lg focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent">
                                                <textarea
                                                    value={inputText}
                                                    onChange={(e) => setInputText(e.target.value)}
                                                    placeholder={t('content.chat.input.placeholder')}
                                                    className="w-full px-3 py-2.5 sm:px-4 sm:py-3 md:px-4 md:py-3 lg:px-5 lg:py-4 pr-12 sm:pr-14 md:pr-16 lg:pr-20 border-0 rounded-lg focus:ring-0 focus:border-transparent outline-none text-sm lg:text-base resize-none h-[44px] sm:h-[48px] md:h-[56px] lg:h-[64px] max-h-32 bg-transparent"
                                                    rows={1}
                                                    disabled={isLoading}
                                                    onInput={(e) => {
                                                        const target = e.target as HTMLTextAreaElement;
                                                        target.style.height = 'auto';
                                                        target.style.height = Math.min(target.scrollHeight, 128) + 'px';
                                                    }}
                                                />
                                                <div className="absolute right-2 sm:right-3 md:right-4 lg:right-5 top-1/2 transform -translate-y-1/2">
                                                    <ImageUpload
                                                        onImageSelect={handleImageSelect}
                                                        onImageRemove={handleImageRemove}
                                                        imagePreview={imagePreview}
                                                        disabled={isLoading}
                                                        compact={true}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex-shrink-0">
                                            <button
                                                type="submit"
                                                disabled={isLoading || (!inputText.trim() && !selectedImage)}
                                                className="h-[44px] sm:h-[48px] md:h-[56px] lg:h-[64px] px-4 md:px-6 lg:px-8 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium text-sm lg:text-base flex items-center justify-center min-w-[80px] md:min-w-[100px] lg:min-w-[120px]"
                                            >
                                                {isLoading ? (
                                                    <div className="flex items-center space-x-1.5">
                                                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                                        <span className="hidden md:inline">{t('content.chat.input.sending')}</span>
                                                    </div>
                                                ) : (
                                                    <div className="flex items-center space-x-1.5">
                                                        <svg className="w-4 h-4 lg:w-5 lg:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                                        </svg>
                                                        <span className="hidden md:inline">{t('content.chat.input.send')}</span>
                                                    </div>
                                                )}
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                {/* Mobile Layout */}
                                <div className="sm:hidden space-y-3">
                                    <div className="relative border border-gray-300 rounded-lg focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent">
                                        <textarea
                                            value={inputText}
                                            onChange={(e) => setInputText(e.target.value)}
                                            placeholder={t('content.chat.input.placeholder')}
                                            className="w-full px-3 py-2.5 pr-12 border-0 rounded-lg focus:ring-0 focus:border-transparent outline-none text-sm resize-none h-[44px] max-h-32 bg-transparent"
                                            rows={1}
                                            disabled={isLoading}
                                            onInput={(e) => {
                                                const target = e.target as HTMLTextAreaElement;
                                                target.style.height = 'auto';
                                                target.style.height = Math.min(target.scrollHeight, 128) + 'px';
                                            }}
                                        />
                                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                            <ImageUpload
                                                onImageSelect={handleImageSelect}
                                                onImageRemove={handleImageRemove}
                                                imagePreview={imagePreview}
                                                disabled={isLoading}
                                                compact={true}
                                            />
                                        </div>
                                    </div>
                                    <button
                                        type="submit"
                                        disabled={isLoading || (!inputText.trim() && !selectedImage)}
                                        className="w-full h-[44px] px-4 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium text-sm flex items-center justify-center"
                                    >
                                        {isLoading ? (
                                            <div className="flex items-center space-x-2">
                                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                                <span>{t('content.chat.input.sending')}</span>
                                            </div>
                                        ) : (
                                            <div className="flex items-center space-x-2">
                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                                </svg>
                                                <span>{t('content.chat.input.send')}</span>
                                            </div>
                                        )}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <Footer />
        </div>
    );
}

import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getBaseUrl } from '@/utils/metadata';
import Image from 'next/image';
import PerformanceChart from '@/components/PerformanceChart';
import MedGemmaHeader from '@/components/MedGemmaHeader';
import { Link, Pathnames } from '@/i18n/routing';
import Footer from '@/components/ui/Footer';

export async function generateMetadata({
    params
}: {
    params: Promise<{ locale: string }>
}): Promise<Metadata> {
    const resolvedParams = await params;
    const locale = resolvedParams.locale;

    const t = await getTranslations('pages.medgemma.metadata');
    const baseUrl = await getBaseUrl();

    return {
        title: t('title'),
        description: t('description'),
        keywords: t('keywords'),
        openGraph: {
            title: t('title'),
            description: t('description'),
            type: 'website',
            url: `${baseUrl}${locale === 'en' ? '' : `/${locale}`}`,
        }
    };
}

export default async function Home() {
    const t = await getTranslations('pages.medgemma.content');

    const faqItems = t.raw('faq.items') as Array<{ question: string; answer: string }>;

    return (
        <div className="min-h-screen bg-white">
            {/* MedGemma Header */}
            <MedGemmaHeader />

            {/* Hero Section */}
            <section className="pt-24 md:pt-28 pb-12 md:pb-16 bg-gradient-to-b from-blue-100 to-white relative overflow-hidden">
                {/* Background Image */}
                <div className="absolute inset-0 z-0">
                    <Image
                        src="/bg.png"
                        alt="Medical AI Background"
                        fill
                        className="object-cover opacity-10"
                        priority
                    />
                </div>

                {/* Content */}
                <div className="max-w-screen-lg mx-auto px-4 relative z-10">
                    <div className="flex flex-col md:flex-row items-center">
                        <div className="md:w-1/2 mb-8 md:mb-0 text-center md:text-left">
                            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-blue-800 mb-4">
                                Med<span className="text-blue-600">Gemma</span>
                            </h1>
                            <h2 className="text-xl sm:text-2xl md:text-3xl font-medium text-blue-700 mb-6">
                                {t('hero.subtitle')}
                            </h2>
                            <p className="text-base sm:text-lg text-gray-700">
                                {t('hero.description')}
                            </p>
                        </div>
                        <div className="md:w-1/2 flex justify-center w-full">
                            <div className="relative max-w-sm md:max-w-none">
                                <div className="absolute -top-4 md:-top-8 -left-4 md:-left-8 w-32 h-32 md:w-48 md:h-48 rounded-full bg-gradient-to-br from-blue-500/90 to-blue-500/40 z-0"></div>
                                <div className="relative z-10 bg-white rounded-lg shadow-lg p-4 md:p-6 w-full">
                                    <div className="flex items-center mb-4">
                                        <div className="text-3xl md:text-5xl text-blue-600 mr-3 md:mr-4">🧠</div>
                                        <div>
                                            <div className="text-5xl md:text-7xl font-bold text-blue-800">2</div>
                                            <div className="text-blue-600 font-medium text-sm md:text-base">
                                                {t('hero.modelVariants')}
                                            </div>
                                        </div>
                                    </div>
                                    <div className="grid grid-cols-2 gap-3 md:gap-4 mt-4">
                                        <div className="bg-blue-50 p-2 md:p-3 rounded-md">
                                            <div className="text-2xl md:text-4xl font-bold text-blue-800">4B</div>
                                            <div className="text-xs md:text-sm">{t('hero.multimodalModel')}</div>
                                        </div>
                                        <div className="bg-blue-50 p-2 md:p-3 rounded-md">
                                            <div className="text-2xl md:text-4xl font-bold text-blue-800">27B</div>
                                            <div className="text-xs md:text-sm">{t('hero.textOnlyModel')}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Demo Section */}
            <section className="py-12 md:py-16 bg-blue-50">
                <div className="max-w-full mx-auto px-2 md:px-4">
                    <div className="text-center mb-6 md:mb-8">
                        <h2 className="text-2xl md:text-3xl font-bold text-blue-800 mb-4">
                            {t('demo.title')}
                        </h2>
                        <p className="text-base md:text-lg text-gray-700">
                            {t('demo.description')}
                        </p>
                    </div>
                    <div className="flex justify-center">
                        <div className="bg-white rounded-xl shadow-lg p-2 md:p-4 border border-blue-200" style={{ width: '95vw', maxWidth: '1200px' }}>
                            <iframe
                                src="https://warshanks-medgemma-4b-it.hf.space"
                                className="rounded-lg w-full"
                                style={{ height: '800px', minHeight: '800px' }}
                                title="MedGemma Interactive Demo"
                            />
                        </div>
                    </div>
                </div>
            </section>

            {/* What is MedGemma Section */}
            <section id="what-is-medgemma" className="py-12 md:py-16">
                <div className="max-w-screen-lg mx-auto px-4">
                    <h2 className="text-3xl md:text-4xl font-bold text-blue-800 mb-6 md:mb-8">
                        {t('whatIs.title')}
                    </h2>
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 md:gap-8">
                        <div className="lg:col-span-2">
                            <p className="text-base md:text-lg mb-4">
                                <strong>MedGemma</strong> {t('whatIs.description1')}
                            </p>

                            <p className="text-base md:text-lg mb-4">
                                {t('whatIs.description2')}
                            </p>

                            <p className="text-base md:text-lg">
                                {t('whatIs.description3')}
                            </p>
                        </div>
                        <div className="bg-gradient-to-br from-blue-500/90 to-blue-500/40 rounded-xl p-4 md:p-6 text-white">
                            <div className="mb-4">
                                <div className="text-xl md:text-2xl">📅</div>
                                <h3 className="text-lg md:text-xl font-bold mt-2">
                                    {t('whatIs.recentDevelopment')}
                                </h3>
                            </div>
                            <p className="text-sm md:text-base">{t('whatIs.launchedAt')}</p>
                            <div className="mt-4 md:mt-6">
                                <div className="text-4xl md:text-5xl font-bold">May</div>
                                <div className="text-2xl md:text-3xl font-medium">2025</div>
                            </div>
                            <div className="mt-4 md:mt-6 text-xs md:text-sm">
                                <p>{t('whatIs.releaseNote')}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <section id="features" className="py-16 bg-gray-50">
                <div className="max-w-screen-lg mx-auto px-4">
                    <h2 className="text-4xl font-bold text-blue-800 mb-2">
                        {t('features.title')}
                    </h2>
                    <p className="text-xl text-gray-600 mb-12">
                        {t('features.subtitle')}
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
                        {/* Model Variants */}
                        <div className="bg-white rounded-lg shadow-md p-6 border-t-4 border-blue-500">
                            <h3 className="text-2xl font-bold text-blue-800 mb-4">
                                {t('features.modelVariants.title')}
                            </h3>
                            <div className="space-y-6">
                                <div className="flex">
                                    <div className="mr-4">
                                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500/90 to-blue-500/40 flex items-center justify-center">
                                            <span className="text-white text-xl">🖼️</span>
                                        </div>
                                    </div>
                                    <div>
                                        <h4 className="text-xl font-semibold text-blue-700">
                                            {t('features.modelVariants.multimodal.title')}
                                        </h4>
                                        <p>{t('features.modelVariants.multimodal.description')}</p>
                                    </div>
                                </div>
                                <div className="flex">
                                    <div className="mr-4">
                                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-700/90 to-blue-700/40 flex items-center justify-center">
                                            <span className="text-white text-xl">📄</span>
                                        </div>
                                    </div>
                                    <div>
                                        <h4 className="text-xl font-semibold text-blue-700">
                                            {t('features.modelVariants.textOnly.title')}
                                        </h4>
                                        <p>{t('features.modelVariants.textOnly.description')}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Capabilities */}
                        <div className="bg-white rounded-lg shadow-md p-6 border-t-4 border-blue-500">
                            <h3 className="text-2xl font-bold text-blue-800 mb-4">
                                {t('features.capabilities.title')}
                            </h3>
                            <ul className="space-y-3">
                                {(t.raw('features.capabilities.items') as string[]).map((item, index) => (
                                    <li key={index} className="flex items-start">
                                        <span className="text-blue-500 mt-1 mr-2">✓</span>
                                        <span>{item}</span>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>

                    {/* Performance Chart */}
                    <div className="bg-white rounded-lg shadow-md p-6 mb-12">
                        <h3 className="text-2xl font-bold text-blue-800 mb-6">
                            {t('features.performance.title')}
                        </h3>
                        <div className="h-64">
                            <PerformanceChart className="h-full" />
                        </div>
                    </div>

                    {/* Use Cases */}
                    <h3 className="text-3xl font-bold text-blue-700 mb-6">
                        {t('features.useCases.title')}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="bg-white rounded-lg shadow-md p-5 border-l-4 border-blue-500">
                            <div className="text-blue-500 text-3xl mb-3">🏥</div>
                            <h4 className="text-xl font-semibold text-blue-800 mb-2">
                                {t('features.useCases.healthcare.title')}
                            </h4>
                            <p>{t('features.useCases.healthcare.description')}</p>
                        </div>

                        <div className="bg-white rounded-lg shadow-md p-5 border-l-4 border-blue-500">
                            <div className="text-blue-500 text-3xl mb-3">🔬</div>
                            <h4 className="text-xl font-semibold text-blue-800 mb-2">
                                {t('features.useCases.research.title')}
                            </h4>
                            <p>{t('features.useCases.research.description')}</p>
                        </div>

                        <div className="bg-white rounded-lg shadow-md p-5 border-l-4 border-blue-500">
                            <div className="text-blue-500 text-3xl mb-3">👨‍⚕️</div>
                            <h4 className="text-xl font-semibold text-blue-800 mb-2">
                                {t('features.useCases.clinical.title')}
                            </h4>
                            <p>{t('features.useCases.clinical.description')}</p>
                        </div>
                    </div>
                </div>
            </section>

            {/* How to Use Section */}
            <section id="how-to-use" className="py-12 md:py-16">
                <div className="max-w-screen-lg mx-auto px-4">
                    <h2 className="text-3xl md:text-4xl font-bold text-blue-800 mb-2">
                        {t('howToUse.title')}
                    </h2>
                    <p className="text-lg md:text-xl text-gray-600 mb-8 md:mb-12">
                        {t('howToUse.subtitle')}
                    </p>

                    {/* Implementation Steps */}
                    <div className="relative mb-12 md:mb-16">
                        <div className="hidden md:block absolute left-8 top-0 h-full w-1 bg-blue-200"></div>

                        <div className="relative mb-8 md:mb-12">
                            <div className="flex flex-col md:flex-row">
                                <div className="flex-shrink-0 z-10 mb-4 md:mb-0">
                                    <div className="w-12 h-12 md:w-16 md:h-16 rounded-full bg-gradient-to-br from-blue-500/90 to-blue-500/40 flex items-center justify-center shadow-lg mx-auto md:mx-0">
                                        <span className="text-white text-lg md:text-2xl font-bold">1</span>
                                    </div>
                                </div>
                                <div className="md:ml-8 md:pt-3">
                                    <h3 className="text-xl md:text-2xl font-bold text-blue-800 mb-3 text-center md:text-left">
                                        {t('howToUse.steps.access.title')}
                                    </h3>
                                    <p className="text-base md:text-lg mb-4">
                                        {t('howToUse.steps.access.description')}
                                    </p>
                                    <div className="bg-gray-50 rounded-lg p-3 md:p-4 border border-gray-200 overflow-x-auto">
                                        <code className="text-blue-800 text-xs md:text-sm">
                                            # Example Python code to load MedGemma model<br />
                                            from transformers import AutoTokenizer, AutoModelForCausalLM<br /><br />
                                            tokenizer = AutoTokenizer.from_pretrained(&quot;google/medgemma-4b-it&quot;)<br />
                                            model = AutoModelForCausalLM.from_pretrained(&quot;google/medgemma-4b-it&quot;)
                                        </code>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="relative mb-8 md:mb-12">
                            <div className="flex flex-col md:flex-row">
                                <div className="flex-shrink-0 z-10 mb-4 md:mb-0">
                                    <div className="w-12 h-12 md:w-16 md:h-16 rounded-full bg-gradient-to-br from-blue-700/90 to-blue-700/40 flex items-center justify-center shadow-lg mx-auto md:mx-0">
                                        <span className="text-white text-lg md:text-2xl font-bold">2</span>
                                    </div>
                                </div>
                                <div className="md:ml-8 md:pt-3">
                                    <h3 className="text-xl md:text-2xl font-bold text-blue-800 mb-3 text-center md:text-left">
                                        {t('howToUse.steps.adaptation.title')}
                                    </h3>
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-4 mb-4">
                                        <div className="bg-white p-3 md:p-4 rounded-lg shadow-sm border border-gray-100">
                                            <h4 className="font-semibold text-blue-700 mb-2 text-sm md:text-base">
                                                {t('howToUse.steps.adaptation.promptEngineering.title')}
                                            </h4>
                                            <p className="text-sm md:text-base">
                                                {t('howToUse.steps.adaptation.promptEngineering.description')}
                                            </p>
                                        </div>
                                        <div className="bg-white p-3 md:p-4 rounded-lg shadow-sm border border-gray-100">
                                            <h4 className="font-semibold text-blue-700 mb-2 text-sm md:text-base">
                                                {t('howToUse.steps.adaptation.fineTuning.title')}
                                            </h4>
                                            <p className="text-sm md:text-base">
                                                {t('howToUse.steps.adaptation.fineTuning.description')}
                                            </p>
                                        </div>
                                        <div className="bg-white p-3 md:p-4 rounded-lg shadow-sm border border-gray-100">
                                            <h4 className="font-semibold text-blue-700 mb-2 text-sm md:text-base">
                                                {t('howToUse.steps.adaptation.agenticOrchestration.title')}
                                            </h4>
                                            <p className="text-sm md:text-base">
                                                {t('howToUse.steps.adaptation.agenticOrchestration.description')}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="relative">
                            <div className="flex flex-col md:flex-row">
                                <div className="flex-shrink-0 z-10 mb-4 md:mb-0">
                                    <div className="w-12 h-12 md:w-16 md:h-16 rounded-full bg-gradient-to-br from-blue-500/90 to-blue-500/40 flex items-center justify-center shadow-lg mx-auto md:mx-0">
                                        <span className="text-white text-lg md:text-2xl font-bold">3</span>
                                    </div>
                                </div>
                                <div className="md:ml-8 md:pt-3">
                                    <h3 className="text-xl md:text-2xl font-bold text-blue-800 mb-3 text-center md:text-left">
                                        {t('howToUse.steps.deployment.title')}
                                    </h3>
                                    <p className="text-base md:text-lg mb-4">
                                        {t('howToUse.steps.deployment.description')}
                                    </p>
                                    <div className="flex flex-col md:flex-row gap-4">
                                        <div className="flex-1 bg-white p-4 md:p-5 rounded-lg shadow-md border border-gray-200">
                                            <div className="text-blue-600 text-2xl md:text-3xl mb-3">💻</div>
                                            <h4 className="text-lg md:text-xl font-semibold text-blue-700 mb-2">
                                                {t('howToUse.steps.deployment.local.title')}
                                            </h4>
                                            <p className="text-sm md:text-base">
                                                {t('howToUse.steps.deployment.local.description')}
                                            </p>
                                        </div>
                                        <div className="flex-1 bg-white p-4 md:p-5 rounded-lg shadow-md border border-gray-200">
                                            <div className="text-blue-600 text-2xl md:text-3xl mb-3">☁️</div>
                                            <h4 className="text-lg md:text-xl font-semibold text-blue-700 mb-2">
                                                {t('howToUse.steps.deployment.cloud.title')}
                                            </h4>
                                            <p className="text-sm md:text-base">
                                                {t('howToUse.steps.deployment.cloud.description')}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Considerations */}
                    <div className="bg-blue-50 rounded-xl p-8 border-l-4 border-blue-700">
                        <h3 className="text-2xl font-bold text-blue-800 mb-4">
                            {t('howToUse.considerations.title')}
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 className="text-xl font-semibold text-blue-700 mb-2">
                                    {t('howToUse.considerations.validation.title')}
                                </h4>
                                <p>{t('howToUse.considerations.validation.description')}</p>
                            </div>
                            <div>
                                <h4 className="text-xl font-semibold text-blue-700 mb-2">
                                    {t('howToUse.considerations.terms.title')}
                                </h4>
                                <p>{t('howToUse.considerations.terms.description')}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* FAQ Section */}
            <section id="faq" className="py-16 bg-gray-50">
                <div className="max-w-screen-lg mx-auto px-4">
                    <h2 className="text-4xl font-bold text-blue-800 mb-2">
                        {t('faq.title')}
                    </h2>
                    <p className="text-xl text-gray-600 mb-12">
                        {t('faq.subtitle')}
                    </p>

                    <div className="space-y-6">
                        {faqItems.map((item, index) => (
                            <div key={index} className="bg-white rounded-lg shadow-sm p-6">
                                <h3 className="text-xl font-bold text-blue-700 mb-2">
                                    {item.question}
                                </h3>
                                <p>{item.answer}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Footer */}
            <Footer />
        </div>
    );
}
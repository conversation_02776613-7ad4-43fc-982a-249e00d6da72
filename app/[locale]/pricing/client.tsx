'use client';

import { useTranslations } from 'next-intl';
import { useSession } from 'next-auth/react';
import { useState } from 'react';
import Image from 'next/image';
import MedGemmaHeader from '@/components/MedGemmaHeader';
import Footer from '@/components/ui/Footer';
import { useStripeSubscription, PlanType, BillingInterval } from '@/hooks/useStripeSubscription';

export default function PricingClient() {
    const t = useTranslations('pages.pricing.content');
    const { data: session } = useSession();
    const { createCheckoutSession, isLoading, error } = useStripeSubscription();
    const [selectedInterval, setSelectedInterval] = useState<BillingInterval>('monthly');

    const plans = [
        {
            key: 'free',
            popular: false
        },
        {
            key: 'standard',
            popular: false
        },
        {
            key: 'pro',
            popular: true
        },
        {
            key: 'clinical',
            popular: false
        }
    ];

    const features = [
        'upload',
        'chat',
        'findings',
        'pdf',
        'image',
        'history',
        'database',
        'tools',
        'export',
        'usage'
    ];

    // 检查功能是否即将推出
    const isComingSoon = (feature: string) => {
        try {
            const translation = t(`features.comingSoon.${feature}`);
            // 如果翻译不存在，会返回key本身，所以检查是否包含key
            return !translation.includes(`features.comingSoon.${feature}`);
        } catch {
            return false;
        }
    };

    const handleSubscribe = async (planKey: string) => {
        if (!session) {
            // 重定向到登录页面
            window.location.href = '/api/auth/signin';
            return;
        }

        if (planKey === 'free') {
            // 免费计划，重定向到AI诊断页面
            window.location.href = '/ai-medical-diagnosis';
            return;
        }

        if (planKey === 'clinical') {
            // 联系销售
            window.location.href = 'mailto:<EMAIL>';
            return;
        }

        // 付费计划，创建Stripe checkout session
        await createCheckoutSession(planKey as PlanType, selectedInterval);
    };

    return (
        <div className="min-h-screen bg-white">
            {/* MedGemma Header */}
            <MedGemmaHeader />

            {/* Hero Section */}
            <section className="pt-32 md:pt-36 pb-12 md:pb-16 bg-gradient-to-b from-blue-100 to-white relative overflow-hidden">
                {/* Background Image */}
                <div className="absolute inset-0 z-0">
                    <Image
                        src="/bg.png"
                        alt="Medical AI Background"
                        fill
                        className="object-cover opacity-5"
                        priority
                    />
                </div>

                {/* Content */}
                <div className="max-w-screen-xl mx-auto px-4 relative z-10">
                    <div className="text-center space-y-4 sm:space-y-6">
                        <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-4">
                            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                            </svg>
                            {t('pricingPlans')}
                        </div>
                        <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-blue-800 mb-4">
                            {t('title')}
                        </h1>
                        <p className="text-lg sm:text-xl text-blue-600 max-w-3xl mx-auto leading-relaxed">
                            {t('subtitle')}
                        </p>
                    </div>
                </div>
            </section>

            {/* Billing Toggle */}
            <section className="py-8">
                <div className="max-w-screen-xl mx-auto px-4">
                    <div className="flex justify-center mb-8">
                        <div className="bg-gray-100 p-1 rounded-lg">
                            <button
                                onClick={() => setSelectedInterval('monthly')}
                                className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                                    selectedInterval === 'monthly'
                                        ? 'bg-white text-blue-600 shadow-sm'
                                        : 'text-gray-600 hover:text-gray-900'
                                }`}
                            >
                                Monthly
                            </button>
                            <button
                                onClick={() => setSelectedInterval('yearly')}
                                className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                                    selectedInterval === 'yearly'
                                        ? 'bg-white text-blue-600 shadow-sm'
                                        : 'text-gray-600 hover:text-gray-900'
                                }`}
                            >
                                Yearly <span className="text-green-600 text-xs ml-1">Save 20%</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            {/* Error Display */}
            {error && (
                <div className="max-w-screen-xl mx-auto px-4 mb-8">
                    <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                        {error}
                    </div>
                </div>
            )}

            {/* Pricing Plans Section */}
            <section className="pb-12 md:pb-16">
                <div className="max-w-screen-xl mx-auto px-4">
                    {/* Plans Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 mb-12 md:mb-16">
                        {plans.map((plan) => (
                            <div
                                key={plan.key}
                                className={`relative bg-white rounded-2xl p-6 md:p-8 shadow-lg border-2 transition-all duration-300 hover:shadow-xl ${
                                    plan.popular 
                                        ? 'border-blue-500 ring-2 ring-blue-200' 
                                        : 'border-blue-100 hover:border-blue-200'
                                }`}
                            >
                                {plan.popular && (
                                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                        <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                                            {t('popular')}
                                        </span>
                                    </div>
                                )}
                                
                                <div className="text-center">
                                    <h3 className="text-xl md:text-2xl font-bold text-blue-800 mb-2">
                                        {t(`plans.${plan.key}.name`)}
                                        {plan.key === 'clinical' && (
                                            <span className="text-sm text-gray-500 ml-2">
                                                {t(`plans.${plan.key}.note`)}
                                            </span>
                                        )}
                                    </h3>
                                    <div className="mb-4">
                                        <span className="text-3xl md:text-4xl font-bold text-blue-600">
                                            {plan.key === 'free' || plan.key === 'clinical' 
                                                ? t(`plans.${plan.key}.price`)
                                                : selectedInterval === 'yearly'
                                                    ? plan.key === 'standard' ? '$7.99' : '$39.99'
                                                    : t(`plans.${plan.key}.price`)
                                            }
                                        </span>
                                        <span className="text-gray-500">
                                            {plan.key === 'free' 
                                                ? '' 
                                                : plan.key === 'clinical'
                                                    ? t(`plans.${plan.key}.period`)
                                                    : selectedInterval === 'yearly' ? '/year' : '/month'
                                            }
                                        </span>
                                    </div>
                                    <p className="text-gray-600 mb-6">
                                        {t(`plans.${plan.key}.target`)}
                                    </p>
                                    <button 
                                        onClick={() => handleSubscribe(plan.key)}
                                        disabled={isLoading}
                                        className={`w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${
                                            plan.popular
                                                ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg'
                                                : 'bg-blue-50 hover:bg-blue-100 text-blue-600 border border-blue-200'
                                        }`}
                                    >
                                        {isLoading ? 'Loading...' : 
                                         plan.key === 'free' ? t('cta.getStarted') : 
                                         plan.key === 'clinical' ? t('cta.contact') : 
                                         t('cta.choosePlan')}
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Features Comparison Section */}
            <section className="py-12 md:py-16 bg-gray-50">
                <div className="max-w-screen-xl mx-auto px-4">
                    <div className="text-center mb-8 md:mb-12">
                        <h2 className="text-3xl md:text-4xl font-bold text-blue-800 mb-4">
                            {t('features.title')}
                        </h2>
                    </div>

                    {/* Desktop Table */}
                    <div className="hidden lg:block overflow-x-auto">
                        <table className="w-full bg-white rounded-2xl shadow-lg overflow-hidden">
                            <thead className="bg-blue-600 text-white">
                                <tr>
                                    <th className="px-6 py-4 text-left font-medium">{t('features.tableHeader')}</th>
                                    <th className="px-6 py-4 text-center font-medium">{t('plans.free.name')}</th>
                                    <th className="px-6 py-4 text-center font-medium">{t('plans.standard.name')}</th>
                                    <th className="px-6 py-4 text-center font-medium">{t('plans.pro.name')}</th>
                                    <th className="px-6 py-4 text-center font-medium">{t('plans.clinical.name')}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {features.map((feature, index) => (
                                    <tr key={feature} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                                        <td className="px-6 py-4 font-medium text-gray-900">
                                            <div className="space-y-1">
                                                <div>{t(`features.categories.${feature}`)}</div>
                                                {isComingSoon(feature) && (
                                                    <div>
                                                        <span className="inline-block px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full font-medium">
                                                            🔜 {t(`features.comingSoon.${feature}`)}
                                                        </span>
                                                    </div>
                                                )}
                                            </div>
                                        </td>
                                        {plans.map((plan) => (
                                            <td key={plan.key} className="px-6 py-4 text-center text-sm">
                                                <div className={`${
                                                    t(`features.plans.${plan.key}.${feature}`).includes('❌')
                                                        ? 'text-red-600'
                                                        : t(`features.plans.${plan.key}.${feature}`).includes('✅')
                                                        ? 'text-green-600'
                                                        : 'text-gray-600'
                                                }`}>
                                                    {t(`features.plans.${plan.key}.${feature}`)}
                                                </div>
                                            </td>
                                        ))}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {/* Mobile Cards */}
                    <div className="lg:hidden space-y-6">
                        {plans.map((plan) => (
                            <div key={plan.key} className="bg-white rounded-2xl p-6 shadow-lg">
                                <div className="text-center mb-6">
                                    <h3 className="text-xl font-bold text-blue-800 mb-2">
                                        {t(`plans.${plan.key}.name`)}
                                    </h3>
                                    <div className="text-2xl font-bold text-blue-600">
                                        {plan.key === 'free' || plan.key === 'clinical'
                                            ? t(`plans.${plan.key}.price`)
                                            : selectedInterval === 'yearly'
                                                ? plan.key === 'standard' ? '$7.99' : '$39.99'
                                                : t(`plans.${plan.key}.price`)
                                        }
                                        <span className="text-sm text-gray-500">
                                            {plan.key === 'free'
                                                ? ''
                                                : plan.key === 'clinical'
                                                    ? t(`plans.${plan.key}.period`)
                                                    : selectedInterval === 'yearly' ? '/year' : '/month'
                                            }
                                        </span>
                                    </div>
                                </div>
                                <div className="space-y-3">
                                    {features.map((feature) => (
                                        <div key={feature} className="flex justify-between items-start gap-4">
                                            <div className="flex-1 min-w-0">
                                                <div className="space-y-1">
                                                    <div className="text-sm font-medium text-gray-700 break-words">
                                                        {t(`features.categories.${feature}`)}
                                                    </div>
                                                    {isComingSoon(feature) && (
                                                        <div>
                                                            <span className="inline-block px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full font-medium">
                                                                🔜 {t(`features.comingSoon.${feature}`)}
                                                            </span>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                            <span className={`text-sm ml-4 ${
                                                t(`features.plans.${plan.key}.${feature}`).includes('❌')
                                                    ? 'text-red-600'
                                                    : t(`features.plans.${plan.key}.${feature}`).includes('✅')
                                                    ? 'text-green-600'
                                                    : 'text-gray-600'
                                            }`}>
                                                {t(`features.plans.${plan.key}.${feature}`)}
                                            </span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Footer */}
            <Footer />
        </div>
    );
}

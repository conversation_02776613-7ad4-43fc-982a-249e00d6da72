import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { useTranslations } from 'next-intl';
import { getBaseUrl } from '@/utils/metadata';
import MedGemmaHeader from '@/components/MedGemmaHeader';
import Image from 'next/image';
import Footer from '@/components/ui/Footer';

type Props = {
    params: Promise<{ locale: string }>;
};

export async function generateMetadata({
    params
}: Props): Promise<Metadata> {
    const resolvedParams = await params;
    const locale = resolvedParams.locale;

    const t = await getTranslations('pages.pricing.metadata');
    const baseUrl = await getBaseUrl();

    return {
        title: t('title'),
        description: t('description'),
        openGraph: {
            title: t('title'),
            description: t('description'),
            type: 'website',
            url: `${baseUrl}${locale === 'en' ? '' : `/${locale}`}/pricing`,
        }
    };
}

export default function PricingPage() {
    const t = useTranslations('pages.pricing.content');

    const plans = [
        {
            key: 'free',
            popular: false
        },
        {
            key: 'standard',
            popular: false
        },
        {
            key: 'pro',
            popular: true
        },
        {
            key: 'clinical',
            popular: false
        }
    ];

    const features = [
        'upload',
        'chat',
        'findings',
        'pdf',
        'image',
        'history',
        'database',
        'tools',
        'export',
        'usage'
    ];

    return (
        <div className="min-h-screen bg-white">
            {/* MedGemma Header */}
            <MedGemmaHeader />

            {/* Hero Section */}
            <section className="pt-32 md:pt-36 pb-12 md:pb-16 bg-gradient-to-b from-blue-100 to-white relative overflow-hidden">
                {/* Background Image */}
                <div className="absolute inset-0 z-0">
                    <Image
                        src="/bg.png"
                        alt="Medical AI Background"
                        fill
                        className="object-cover opacity-5"
                        priority
                    />
                </div>

                {/* Content */}
                <div className="max-w-screen-xl mx-auto px-4 relative z-10">
                    <div className="text-center space-y-4 sm:space-y-6">
                        <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-4">
                            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                            </svg>
                            Pricing Plans
                        </div>
                        <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-blue-800 mb-4">
                            {t('title')}
                        </h1>
                        <p className="text-lg sm:text-xl text-blue-600 max-w-3xl mx-auto leading-relaxed">
                            {t('subtitle')}
                        </p>
                    </div>
                </div>
            </section>

            {/* Pricing Plans Section */}
            <section className="py-12 md:py-16">
                <div className="max-w-screen-xl mx-auto px-4">
                    {/* Note */}
                    <div className="mb-8 md:mb-12 text-center">
                        <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-lg text-sm">
                            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            {t('note')}
                        </div>
                    </div>

                    {/* Plans Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 mb-12 md:mb-16">
                        {plans.map((plan) => (
                            <div
                                key={plan.key}
                                className={`relative bg-white rounded-2xl p-6 md:p-8 shadow-lg border-2 transition-all duration-300 hover:shadow-xl ${
                                    plan.popular 
                                        ? 'border-blue-500 ring-2 ring-blue-200' 
                                        : 'border-blue-100 hover:border-blue-200'
                                }`}
                            >
                                {plan.popular && (
                                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                        <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                                            Popular
                                        </span>
                                    </div>
                                )}
                                
                                <div className="text-center">
                                    <h3 className="text-xl md:text-2xl font-bold text-blue-800 mb-2">
                                        {t(`plans.${plan.key}.name`)}
                                        {plan.key === 'clinical' && (
                                            <span className="text-sm text-gray-500 ml-2">
                                                {t(`plans.${plan.key}.note`)}
                                            </span>
                                        )}
                                    </h3>
                                    <div className="mb-4">
                                        <span className="text-3xl md:text-4xl font-bold text-blue-600">
                                            {t(`plans.${plan.key}.price`)}
                                        </span>
                                        <span className="text-gray-500">
                                            {t(`plans.${plan.key}.period`)}
                                        </span>
                                    </div>
                                    <p className="text-gray-600 mb-6">
                                        {t(`plans.${plan.key}.target`)}
                                    </p>
                                    <button className={`w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 ${
                                        plan.popular
                                            ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg'
                                            : 'bg-blue-50 hover:bg-blue-100 text-blue-600 border border-blue-200'
                                    }`}>
                                        {plan.key === 'free' ? t('cta.getStarted') : 
                                         plan.key === 'clinical' ? t('cta.contact') : 
                                         t('cta.choosePlan')}
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Features Comparison Section */}
            <section className="py-12 md:py-16 bg-gray-50">
                <div className="max-w-screen-xl mx-auto px-4">
                    <div className="text-center mb-8 md:mb-12">
                        <h2 className="text-3xl md:text-4xl font-bold text-blue-800 mb-4">
                            {t('features.title')}
                        </h2>
                    </div>

                    {/* Desktop Table */}
                    <div className="hidden lg:block overflow-x-auto">
                        <table className="w-full bg-white rounded-2xl shadow-lg overflow-hidden">
                            <thead className="bg-blue-600 text-white">
                                <tr>
                                    <th className="px-6 py-4 text-left font-medium">功能模块</th>
                                    <th className="px-6 py-4 text-center font-medium">Free</th>
                                    <th className="px-6 py-4 text-center font-medium">Standard</th>
                                    <th className="px-6 py-4 text-center font-medium">Pro</th>
                                    <th className="px-6 py-4 text-center font-medium">Clinical+</th>
                                </tr>
                            </thead>
                            <tbody>
                                {features.map((feature, index) => (
                                    <tr key={feature} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                                        <td className="px-6 py-4 font-medium text-gray-900">
                                            {t(`features.categories.${feature}`)}
                                        </td>
                                        {plans.map((plan) => (
                                            <td key={plan.key} className="px-6 py-4 text-center text-sm">
                                                <div className={`${
                                                    t(`features.plans.${plan.key}.${feature}`).includes('❌')
                                                        ? 'text-red-600'
                                                        : t(`features.plans.${plan.key}.${feature}`).includes('✅')
                                                        ? 'text-green-600'
                                                        : 'text-gray-600'
                                                }`}>
                                                    {t(`features.plans.${plan.key}.${feature}`)}
                                                </div>
                                            </td>
                                        ))}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {/* Mobile Cards */}
                    <div className="lg:hidden space-y-6">
                        {plans.map((plan) => (
                            <div key={plan.key} className="bg-white rounded-2xl p-6 shadow-lg">
                                <div className="text-center mb-6">
                                    <h3 className="text-xl font-bold text-blue-800 mb-2">
                                        {t(`plans.${plan.key}.name`)}
                                    </h3>
                                    <div className="text-2xl font-bold text-blue-600">
                                        {t(`plans.${plan.key}.price`)}
                                        <span className="text-sm text-gray-500">
                                            {t(`plans.${plan.key}.period`)}
                                        </span>
                                    </div>
                                </div>
                                <div className="space-y-3">
                                    {features.map((feature) => (
                                        <div key={feature} className="flex justify-between items-start">
                                            <span className="text-sm font-medium text-gray-700 flex-1">
                                                {t(`features.categories.${feature}`)}
                                            </span>
                                            <span className={`text-sm ml-4 ${
                                                t(`features.plans.${plan.key}.${feature}`).includes('❌')
                                                    ? 'text-red-600'
                                                    : t(`features.plans.${plan.key}.${feature}`).includes('✅')
                                                    ? 'text-green-600'
                                                    : 'text-gray-600'
                                            }`}>
                                                {t(`features.plans.${plan.key}.${feature}`)}
                                            </span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Footer */}
            <Footer />
        </div>
    );
}

'use client'

import { useState } from 'react';
import { useTracking } from '@/hooks/useTracking';
import { TrackingEvents, TrackingCategories } from '@/utils/trackingEvents';

export default function TrackingDemoPage() {
    const [message, setMessage] = useState('');
    const [searchTerm, setSearchTerm] = useState('');

    const {
        trackButtonClick,
        trackLinkClick,
        trackFormSubmit,
        trackSearch,
        trackDownload,
        trackError,
        trackAction
        // trackActionViaAPI // 移除API跟踪功能
    } = useTracking();

    const handleButtonClick = () => {
        trackButtonClick('demo-button', '演示按钮');
        setMessage('✅ 按钮点击已跟踪！');
    };

    const handleLinkClick = (href: string, isExternal: boolean) => {
        trackLinkClick(href, '测试链接', isExternal);
        setMessage(`✅ ${isExternal ? '外部' : '内部'}链接点击已跟踪！`);
    };

    const handleFormSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        trackFormSubmit('demo-form', 'search', { searchTerm });
        setMessage('✅ 表单提交已跟踪！');
    };

    const handleSearch = () => {
        if (searchTerm) {
            trackSearch(searchTerm, Math.floor(Math.random() * 10));
            setMessage(`✅ 搜索"${searchTerm}"已跟踪！`);
        }
    };

    const handleDownload = () => {
        trackDownload('demo-file.pdf', 'pdf', 1024000);
        setMessage('✅ 文件下载已跟踪！');
    };

    const handleError = () => {
        trackError('demo_error', '这是一个演示错误', 'tracking_demo_page');
        setMessage('✅ 错误事件已跟踪！');
    };

    const handleCustomEvent = () => {
        trackAction(
            TrackingEvents.MEDGEMMA_DEMO_USE,
            TrackingCategories.BUSINESS,
            'demo_custom_event',
            1,
            {
                customData: '自定义数据',
                timestamp: new Date().toISOString()
            }
        );
        setMessage('✅ 自定义事件已跟踪！');
    };

    // 移除API跟踪和健康检查功能
    // const handleAPITracking = () => {
    //     trackActionViaAPI(
    //         'api_test' as any,
    //         TrackingCategories.API,
    //         'api_tracking_test',
    //         1,
    //         {
    //             method: 'api',
    //             reliable: true
    //         }
    //     );
    //     setMessage('✅ API跟踪已发送！');
    // };

    // const testHealthCheck = async () => {
    //     try {
    //         const response = await fetch('/api/health');
    //         const data = await response.json();
    //         setMessage(`✅ 健康检查: ${data.status} (运行时间: ${Math.floor(data.uptime)}秒)`);
    //     } catch (error) {
    //         setMessage('❌ 健康检查失败');
    //     }
    // };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4">
            <div className="max-w-4xl mx-auto">
                <div className="bg-white rounded-2xl shadow-xl p-8">
                    <h1 className="text-4xl font-bold text-gray-800 text-center mb-2">
                        用户行为跟踪演示
                    </h1>
                    <p className="text-gray-600 text-center mb-8">
                        点击下方按钮体验各种跟踪功能，所有行为都会实时发送到飞书群聊
                    </p>

                    {/* 状态消息 */}
                    {message && (
                        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                            <p className="text-green-800 text-center font-medium">{message}</p>
                        </div>
                    )}

                    {/* 跟踪功能演示区域 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

                        {/* 按钮点击跟踪 */}
                        <div className="bg-gray-50 rounded-lg p-6">
                            <h3 className="text-lg font-semibold mb-4 text-gray-800">按钮点击跟踪</h3>
                            <button
                                onClick={handleButtonClick}
                                className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                            >
                                点击我！
                            </button>
                        </div>

                        {/* 链接点击跟踪 */}
                        <div className="bg-gray-50 rounded-lg p-6">
                            <h3 className="text-lg font-semibold mb-4 text-gray-800">链接点击跟踪</h3>
                            <div className="space-y-2">
                                <button
                                    onClick={() => handleLinkClick('/medgemma', false)}
                                    className="block w-full text-blue-600 hover:text-blue-800 underline text-left"
                                >
                                    内部链接 (/medgemma)
                                </button>
                                <button
                                    onClick={() => handleLinkClick('https://google.com', true)}
                                    className="block w-full text-blue-600 hover:text-blue-800 underline text-left"
                                >
                                    外部链接 (Google)
                                </button>
                            </div>
                        </div>

                        {/* 搜索跟踪 */}
                        <div className="bg-gray-50 rounded-lg p-6">
                            <h3 className="text-lg font-semibold mb-4 text-gray-800">搜索跟踪</h3>
                            <form onSubmit={handleFormSubmit} className="space-y-3">
                                <input
                                    type="text"
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    placeholder="输入搜索关键词"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                                <button
                                    type="button"
                                    onClick={handleSearch}
                                    className="w-full bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                                >
                                    搜索
                                </button>
                            </form>
                        </div>

                        {/* 下载跟踪 */}
                        <div className="bg-gray-50 rounded-lg p-6">
                            <h3 className="text-lg font-semibold mb-4 text-gray-800">下载跟踪</h3>
                            <button
                                onClick={handleDownload}
                                className="w-full bg-purple-500 hover:bg-purple-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                            >
                                下载文件
                            </button>
                        </div>

                        {/* 错误跟踪 */}
                        <div className="bg-gray-50 rounded-lg p-6">
                            <h3 className="text-lg font-semibold mb-4 text-gray-800">错误跟踪</h3>
                            <button
                                onClick={handleError}
                                className="w-full bg-red-500 hover:bg-red-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                            >
                                触发错误
                            </button>
                        </div>

                        {/* 自定义事件跟踪 */}
                        <div className="bg-gray-50 rounded-lg p-6">
                            <h3 className="text-lg font-semibold mb-4 text-gray-800">自定义事件</h3>
                            <button
                                onClick={handleCustomEvent}
                                className="w-full bg-indigo-500 hover:bg-indigo-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                            >
                                自定义事件
                            </button>
                        </div>

                    </div>

                    {/* API测试区域已移除 */}
                    {/* <div className="mt-8 border-t pt-8">
                        <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">API 功能测试</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            API跟踪和健康检查功能已移除
                        </div>
                    </div> */}

                    {/* 说明信息 */}
                    <div className="mt-8 bg-blue-50 rounded-lg p-6">
                        <h3 className="text-lg font-semibold mb-3 text-blue-800">💡 跟踪功能说明</h3>
                        <ul className="text-blue-700 space-y-2 text-sm">
                            <li>• <strong>自动跟踪</strong>：页面访问、滚动深度、停留时间等行为自动跟踪</li>
                            <li>• <strong>手动跟踪</strong>：按钮点击、链接点击、表单提交等可手动触发</li>
                            <li>• <strong>飞书通知</strong>：客户端跟踪事件会实时发送到配置的飞书群聊</li>
                            <li>• <strong>IP地理位置</strong>：自动获取用户IP地址和地理位置信息（仅客户端）</li>
                            <li>• <strong>设备信息</strong>：记录用户浏览器、屏幕分辨率等设备信息</li>
                            <li>• <strong>客户端跟踪</strong>：专注于用户交互行为的精准跟踪</li>
                        </ul>
                    </div>

                    {/* 技术信息 */}
                    <div className="mt-6 text-center text-gray-500 text-sm">
                        <p>
                            基于 <code className="bg-gray-200 px-2 py-1 rounded">utils/notifyFeishu.ts</code> 和
                            <code className="bg-gray-200 px-2 py-1 rounded ml-1">.env</code> 中的 Webhook 配置
                        </p>
                    </div>

                </div>
            </div>
        </div>
    );
} 
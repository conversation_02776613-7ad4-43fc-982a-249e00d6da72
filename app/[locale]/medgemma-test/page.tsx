'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';

interface MedGeminiResponse {
    response: string;
    model: string;
    usage: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}

interface ApiError {
    error: string;
    details?: any;
}

export default function MedGeminiTestPage() {
    const t = useTranslations('pages.medgemma.content');

    const [prompt, setPrompt] = useState('');
    const [model, setModel] = useState<'gemini-1.5-pro' | 'gemini-1.5-flash' | 'gemini-2.0-flash'>('gemini-1.5-flash');
    const [response, setResponse] = useState<MedGeminiResponse | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [healthStatus, setHealthStatus] = useState<any>(null);

    // 预设的医疗问题示例
    const examplePrompts = [
        "What are the common symptoms of hypertension and how is it diagnosed?",
        "Explain the difference between Type 1 and Type 2 diabetes, including their pathophysiology.",
        "What are the major risk factors for cardiovascular disease and prevention strategies?",
        "Describe the four stages of wound healing and factors that can impair healing.",
        "What are the first-line medications for treating asthma and their mechanisms of action?",
        "Explain the pathophysiology of pneumonia and its typical chest X-ray findings.",
        "What are the clinical manifestations of acute myocardial infarction?",
        "Describe the differential diagnosis for acute abdominal pain in adults."
    ];

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!prompt.trim()) return;

        setIsLoading(true);
        setError(null);
        setResponse(null);

        try {
            const res = await fetch('/api/medgemma', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt: prompt.trim(),
                    model,
                    maxTokens: 1024,
                    temperature: 0.7,
                    topP: 0.9,
                    topK: 40
                }),
            });

            const data = await res.json();

            if (!res.ok) {
                throw new Error(data.error || 'API request failed');
            }

            setResponse(data);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An unexpected error occurred');
        } finally {
            setIsLoading(false);
        }
    };

    const checkHealth = async () => {
        try {
            const res = await fetch('/api/medgemma?health=true');
            const data = await res.json();
            setHealthStatus(data);
        } catch (err) {
            setHealthStatus({
                status: 'error',
                error: err instanceof Error ? err.message : 'Health check failed'
            });
        }
    };

    const useExamplePrompt = (examplePrompt: string) => {
        setPrompt(examplePrompt);
    };

    return (
        <div className="min-h-screen bg-white">
            {/* Header */}
            <div className="bg-gradient-to-r from-green-600 to-blue-700 text-white py-8">
                <div className="max-w-4xl mx-auto px-4">
                    <h1 className="text-3xl md:text-4xl font-bold mb-2">
                        Med Gemini API 测试页面
                    </h1>
                    <p className="text-green-100">
                        测试 Google Vertex AI Med Gemini 模型集成 - 专为医疗应用优化的 Gemini 模型
                    </p>
                </div>
            </div>

            <div className="max-w-4xl mx-auto px-4 py-8">
                {/* Health Check Section */}
                <div className="mb-8 p-6 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between mb-4">
                        <h2 className="text-xl font-semibold text-gray-800">API 健康检查</h2>
                        <button
                            onClick={checkHealth}
                            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                        >
                            检查状态
                        </button>
                    </div>

                    {healthStatus && (
                        <div className={`p-4 rounded-md ${healthStatus.status === 'healthy'
                            ? 'bg-green-100 border border-green-300'
                            : 'bg-red-100 border border-red-300'
                            }`}>
                            <pre className="text-sm overflow-x-auto">
                                {JSON.stringify(healthStatus, null, 2)}
                            </pre>
                        </div>
                    )}
                </div>

                {/* Model Info Section */}
                <div className="mb-8 p-6 bg-blue-50 rounded-lg border border-blue-200">
                    <h2 className="text-xl font-semibold text-blue-800 mb-4">关于 Med Gemini</h2>
                    <div className="text-blue-700 space-y-2 text-sm">
                        <p><strong>Med Gemini</strong> 是基于 Google Gemini 模型针对医疗应用优化的AI助手。</p>
                        <p><strong>与 MedGemma 的区别：</strong> Med Gemini 使用 Vertex AI 上的 Gemini 模型，而 MedGemma 是独立的开源医疗模型。</p>
                        <p><strong>优势：</strong> 更强的推理能力、更好的多模态理解、实时更新的医疗知识。</p>
                    </div>
                </div>

                {/* Example Prompts */}
                <div className="mb-8">
                    <h2 className="text-xl font-semibold text-gray-800 mb-4">示例医疗问题</h2>
                    <div className="grid gap-2">
                        {examplePrompts.map((example, index) => (
                            <button
                                key={index}
                                onClick={() => useExamplePrompt(example)}
                                className="text-left p-3 bg-green-50 hover:bg-green-100 rounded-md border border-green-200 transition-colors text-sm"
                            >
                                {example}
                            </button>
                        ))}
                    </div>
                </div>

                {/* Main Form */}
                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Model Selection */}
                    <div>
                        <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-2">
                            选择 Med Gemini 模型
                        </label>
                        <select
                            id="model"
                            value={model}
                            onChange={(e) => setModel(e.target.value as 'gemini-1.5-pro' | 'gemini-1.5-flash' | 'gemini-2.0-flash')}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        >
                            <option value="gemini-1.5-flash">Gemini 1.5 Flash (推荐 - 平衡性能与成本)</option>
                            <option value="gemini-1.5-pro">Gemini 1.5 Pro (高性能 - 复杂医疗推理)</option>
                            <option value="gemini-2.0-flash">Gemini 2.0 Flash (最新 - 增强功能)</option>
                        </select>
                        <p className="mt-1 text-xs text-gray-500">
                            所有模型都经过医疗应用优化，包含安全过滤和医疗专业提示
                        </p>
                    </div>

                    {/* Prompt Input */}
                    <div>
                        <label htmlFor="prompt" className="block text-sm font-medium text-gray-700 mb-2">
                            医疗问题或提示词
                        </label>
                        <textarea
                            id="prompt"
                            value={prompt}
                            onChange={(e) => setPrompt(e.target.value)}
                            placeholder="请输入您的医疗相关问题，例如：症状分析、疾病诊断、治疗方案等..."
                            rows={4}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                            required
                        />
                    </div>

                    {/* Submit Button */}
                    <button
                        type="submit"
                        disabled={isLoading || !prompt.trim()}
                        className="w-full px-6 py-3 bg-green-600 text-white font-medium rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                    >
                        {isLoading ? '正在处理...' : '发送请求'}
                    </button>
                </form>

                {/* Error Display */}
                {error && (
                    <div className="mt-6 p-4 bg-red-100 border border-red-300 rounded-md">
                        <h3 className="text-lg font-semibold text-red-800 mb-2">错误</h3>
                        <p className="text-red-700">{error}</p>
                    </div>
                )}

                {/* Response Display */}
                {response && (
                    <div className="mt-6 p-6 bg-green-50 border border-green-300 rounded-md">
                        <h3 className="text-lg font-semibold text-green-800 mb-4">Med Gemini 响应</h3>

                        <div className="mb-4 p-4 bg-white rounded-md border border-green-200">
                            <div className="prose max-w-none">
                                <div className="whitespace-pre-wrap text-gray-800">
                                    {response.response}
                                </div>
                            </div>
                        </div>

                        <div className="text-sm text-green-700 space-y-2">
                            <div className="flex items-center justify-between">
                                <span className="font-medium">模型信息</span>
                                <span className="bg-green-200 px-2 py-1 rounded text-xs font-mono">
                                    {response.model}
                                </span>
                            </div>

                            <div className="grid grid-cols-3 gap-4 pt-2">
                                <div>
                                    <span className="text-gray-600">提示长度:</span>
                                    <span className="ml-2 font-medium">{response.usage.promptTokens} 词</span>
                                </div>
                                <div>
                                    <span className="text-gray-600">响应长度:</span>
                                    <span className="ml-2 font-medium">{response.usage.completionTokens} 词</span>
                                </div>
                                <div>
                                    <span className="text-gray-600">总计:</span>
                                    <span className="ml-2 font-medium">{response.usage.totalTokens} 词</span>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Medical Disclaimer */}
                <div className="mt-8 p-6 bg-yellow-50 border border-yellow-300 rounded-md">
                    <div className="flex items-start">
                        <div className="flex-shrink-0 mr-3">
                            <span className="text-yellow-600 text-xl">⚠️</span>
                        </div>
                        <div>
                            <h3 className="text-lg font-semibold text-yellow-800 mb-2">医疗免责声明</h3>
                            <div className="text-yellow-700 space-y-1 text-sm">
                                <p><strong>仅供研究使用:</strong> Med Gemini 模型仅用于研究和开发目的，不能用于实际的患者护理。</p>
                                <p><strong>非医疗建议:</strong> AI 生成的内容不构成医疗建议、诊断或治疗方案。</p>
                                <p><strong>咨询专业人士:</strong> 任何医疗决策都应咨询合格的医疗专业人士。</p>
                                <p><strong>需要验证:</strong> 任何临床应用都需要彻底验证和监管合规。</p>
                                <p><strong>模型说明:</strong> 本系统使用 Vertex AI 上的 Gemini 模型，经过医疗应用优化。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
} 
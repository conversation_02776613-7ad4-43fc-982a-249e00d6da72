'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useSession } from 'next-auth/react';
import MedGemmaHeader from '@/components/MedGemmaHeader';
import Footer from '@/components/ui/Footer';
import { Link, Pathnames } from '@/i18n/routing';

interface UserSubscription {
    plan: 'FREE' | 'STANDARD' | 'PRO' | 'CLINICAL_PLUS';
    status: 'ACTIVE' | 'INACTIVE' | 'CANCELLED' | 'EXPIRED';
    endDate?: string;
    dailyUploadLimit: number;
    chatRoundsLimit: number;
    historyDaysLimit: number;
    hasStructuredFindings: boolean;
    hasPdfExport: boolean;
    hasImageAnalysis: boolean;
    hasTeachingTools: boolean;
    hasBatchUpload: boolean;
}

interface UsageStats {
    dailyUploads: number;
    monthlyUploads: number;
    totalDiagnoses: number;
    totalMessages: number;
    totalImages: number;
    pdfExports: number;
    teachingExports: number;
}

interface DiagnosisRecord {
    id: string;
    title: string;
    status: 'PENDING' | 'COMPLETED' | 'FAILED';
    type: 'TEXT_ONLY' | 'IMAGE_ONLY' | 'MULTIMODAL';
    riskLevel?: 'LOW' | 'MEDIUM' | 'HIGH';
    createdAt: string;
}

export default function ProfileClient() {
    const t = useTranslations('pages.profile.content');
    const { data: session } = useSession();
    const [subscription, setSubscription] = useState<UserSubscription | null>(null);
    const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
    const [recentDiagnoses, setRecentDiagnoses] = useState<DiagnosisRecord[]>([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Mock data for demonstration
        // In real implementation, fetch from API
        setSubscription({
            plan: 'FREE',
            status: 'ACTIVE',
            dailyUploadLimit: 5,
            chatRoundsLimit: 0,
            historyDaysLimit: 0,
            hasStructuredFindings: false,
            hasPdfExport: false,
            hasImageAnalysis: false,
            hasTeachingTools: false,
            hasBatchUpload: false
        });

        setUsageStats({
            dailyUploads: 2,
            monthlyUploads: 15,
            totalDiagnoses: 8,
            totalMessages: 24,
            totalImages: 5,
            pdfExports: 0,
            teachingExports: 0
        });

        setRecentDiagnoses([
            {
                id: '1',
                title: 'Chest X-ray Analysis',
                status: 'COMPLETED',
                type: 'IMAGE_ONLY',
                riskLevel: 'LOW',
                createdAt: new Date(Date.now() - 86400000).toISOString()
            },
            {
                id: '2',
                title: 'Skin Condition Assessment',
                status: 'COMPLETED',
                type: 'MULTIMODAL',
                riskLevel: 'MEDIUM',
                createdAt: new Date(Date.now() - 172800000).toISOString()
            }
        ]);

        setLoading(false);
    }, []);

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString();
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'ACTIVE': return 'text-green-600 bg-green-100';
            case 'EXPIRED': return 'text-red-600 bg-red-100';
            case 'CANCELLED': return 'text-gray-600 bg-gray-100';
            default: return 'text-yellow-600 bg-yellow-100';
        }
    };

    const getRiskColor = (risk?: string) => {
        switch (risk) {
            case 'LOW': return 'text-green-600 bg-green-100';
            case 'MEDIUM': return 'text-yellow-600 bg-yellow-100';
            case 'HIGH': return 'text-red-600 bg-red-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50">
                <MedGemmaHeader />
                <div className="pt-32 pb-16">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center justify-center h-64">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                        </div>
                    </div>
                </div>
                <Footer />
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <MedGemmaHeader />
            
            {/* Hero Section */}
            <section className="pt-28 sm:pt-32 md:pt-36 pb-8 bg-gradient-to-b from-blue-100 to-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-blue-800 mb-4">
                            {t('title')}
                        </h1>
                        <p className="text-lg md:text-xl text-blue-700 max-w-2xl mx-auto">
                            {t('subtitle')}
                        </p>
                    </div>
                </div>
            </section>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Left Column - Subscription & Features */}
                    <div className="lg:col-span-2 space-y-8">
                        {/* Subscription Card */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <h2 className="text-2xl font-bold text-gray-800 mb-6">{t('subscription.title')}</h2>
                            {subscription && (
                                <div className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <span className="text-gray-600">{t('subscription.plan')}</span>
                                        <span className="font-semibold text-blue-600">
                                            {t(`subscription.plans.${subscription.plan}`)}
                                        </span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-gray-600">{t('subscription.status')}</span>
                                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(subscription.status)}`}>
                                            {t(`subscription.status.${subscription.status}`)}
                                        </span>
                                    </div>
                                    {subscription.endDate && (
                                        <div className="flex items-center justify-between">
                                            <span className="text-gray-600">{t('subscription.validUntil')}</span>
                                            <span className="font-medium">{formatDate(subscription.endDate)}</span>
                                        </div>
                                    )}
                                    <div className="pt-4 border-t">
                                        <Link
                                            href={"/pricing" as Pathnames}
                                            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                        >
                                            {t('subscription.upgrade')}
                                        </Link>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Features Card */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <h2 className="text-2xl font-bold text-gray-800 mb-6">{t('features.title')}</h2>
                            {subscription && (
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <span className="text-gray-700">{t('features.dailyUploads')}</span>
                                        <span className="font-semibold">
                                            {subscription.dailyUploadLimit === -1 ? t('features.unlimited') : subscription.dailyUploadLimit}
                                        </span>
                                    </div>
                                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <span className="text-gray-700">{t('features.chatRounds')}</span>
                                        <span className="font-semibold">
                                            {subscription.chatRoundsLimit === -1 ? t('features.unlimited') : 
                                             subscription.chatRoundsLimit === 0 ? t('features.disabled') : 
                                             `${subscription.chatRoundsLimit} ${t('features.rounds')}`}
                                        </span>
                                    </div>
                                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <span className="text-gray-700">{t('features.historyDays')}</span>
                                        <span className="font-semibold">
                                            {subscription.historyDaysLimit === -1 ? t('features.unlimited') : 
                                             subscription.historyDaysLimit === 0 ? t('features.disabled') : 
                                             `${subscription.historyDaysLimit} ${t('features.days')}`}
                                        </span>
                                    </div>
                                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <span className="text-gray-700">{t('features.structuredFindings')}</span>
                                        <span className={`font-semibold ${subscription.hasStructuredFindings ? 'text-green-600' : 'text-gray-400'}`}>
                                            {subscription.hasStructuredFindings ? t('features.enabled') : t('features.disabled')}
                                        </span>
                                    </div>
                                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <span className="text-gray-700">{t('features.pdfExport')}</span>
                                        <span className={`font-semibold ${subscription.hasPdfExport ? 'text-green-600' : 'text-gray-400'}`}>
                                            {subscription.hasPdfExport ? t('features.enabled') : t('features.disabled')}
                                        </span>
                                    </div>
                                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <span className="text-gray-700">{t('features.imageAnalysis')}</span>
                                        <span className={`font-semibold ${subscription.hasImageAnalysis ? 'text-green-600' : 'text-gray-400'}`}>
                                            {subscription.hasImageAnalysis ? t('features.enabled') : t('features.disabled')}
                                        </span>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Right Column - Usage Stats & Recent Diagnoses */}
                    <div className="space-y-8">
                        {/* Usage Stats Card */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <h2 className="text-xl font-bold text-gray-800 mb-6">{t('usage.title')}</h2>
                            {usageStats && (
                                <div className="space-y-4">
                                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                                        <div className="text-2xl font-bold text-blue-600">{usageStats.dailyUploads}</div>
                                        <div className="text-sm text-blue-700">{t('usage.today')}</div>
                                    </div>
                                    <div className="grid grid-cols-2 gap-4 text-center">
                                        <div className="p-3 bg-gray-50 rounded-lg">
                                            <div className="text-lg font-semibold">{usageStats.totalDiagnoses}</div>
                                            <div className="text-xs text-gray-600">{t('usage.diagnoses')}</div>
                                        </div>
                                        <div className="p-3 bg-gray-50 rounded-lg">
                                            <div className="text-lg font-semibold">{usageStats.totalMessages}</div>
                                            <div className="text-xs text-gray-600">{t('usage.messages')}</div>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Recent Diagnoses Card */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <div className="flex items-center justify-between mb-6">
                                <h2 className="text-xl font-bold text-gray-800">{t('recentDiagnoses.title')}</h2>
                                <Link
                                    href={"/ai-medical-diagnosis" as Pathnames}
                                    className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                                >
                                    {t('recentDiagnoses.viewAll')}
                                </Link>
                            </div>
                            {recentDiagnoses.length > 0 ? (
                                <div className="space-y-3">
                                    {recentDiagnoses.map((diagnosis) => (
                                        <div key={diagnosis.id} className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                            <div className="flex items-center justify-between mb-2">
                                                <h3 className="font-medium text-gray-800 truncate">{diagnosis.title}</h3>
                                                {diagnosis.riskLevel && (
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(diagnosis.riskLevel)}`}>
                                                        {t(`recentDiagnoses.riskLevel.${diagnosis.riskLevel}`)}
                                                    </span>
                                                )}
                                            </div>
                                            <div className="flex items-center justify-between text-sm text-gray-500">
                                                <span>{t(`recentDiagnoses.type.${diagnosis.type}`)}</span>
                                                <span>{formatDate(diagnosis.createdAt)}</span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8">
                                    <div className="text-gray-400 mb-2">{t('recentDiagnoses.noRecords')}</div>
                                    <Link
                                        href={"/ai-medical-diagnosis" as Pathnames}
                                        className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                                    >
                                        {t('recentDiagnoses.startFirst')}
                                    </Link>
                                </div>
                            )}
                        </div>

                        {/* Quick Actions */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <div className="space-y-3">
                                <Link
                                    href={"/ai-medical-diagnosis" as Pathnames}
                                    className="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                >
                                    {t('actions.newDiagnosis')}
                                </Link>
                                <Link
                                    href={"/pricing" as Pathnames}
                                    className="w-full inline-flex items-center justify-center px-4 py-2 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
                                >
                                    {t('subscription.upgrade')}
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <Footer />
        </div>
    );
}

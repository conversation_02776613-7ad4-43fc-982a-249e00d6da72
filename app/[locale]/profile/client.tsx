'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useSession } from 'next-auth/react';
import MedGemmaHeader from '@/components/MedGemmaHeader';
import Footer from '@/components/ui/Footer';
import { Link, Pathnames } from '@/i18n/routing';

interface UserSubscription {
    plan: 'FREE' | 'STANDARD' | 'PRO' | 'CLINICAL_PLUS';
    status: 'ACTIVE' | 'INACTIVE' | 'CANCELLED' | 'EXPIRED';
    endDate?: string;
    dailyUploadLimit: number;
    chatRoundsLimit: number;
    historyDaysLimit: number;
    hasStructuredFindings: boolean;
    hasPdfExport: boolean;
    hasImageAnalysis: boolean;
    hasTeachingTools: boolean;
    hasBatchUpload: boolean;
    // Coming soon features for STANDARD plan
    imageAnalysisComingSoon?: boolean;
    teachingToolsComingSoon?: boolean;
}

interface UsageStats {
    dailyUploads: number;
    monthlyUploads: number;
    totalDiagnoses: number;
    totalMessages: number;
    totalImages: number;
    pdfExports: number;
    teachingExports: number;
}

interface DiagnosisRecord {
    id: string;
    title: string;
    status: 'PENDING' | 'COMPLETED' | 'FAILED';
    type: 'TEXT_ONLY' | 'IMAGE_ONLY' | 'MULTIMODAL';
    riskLevel?: 'LOW' | 'MEDIUM' | 'HIGH';
    timestamp: Date;
}

export default function ProfileClient() {
    const t = useTranslations('pages.profile.content');
    const { data: session } = useSession();
    const [subscription, setSubscription] = useState<UserSubscription | null>(null);
    const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
    const [recentDiagnoses, setRecentDiagnoses] = useState<DiagnosisRecord[]>([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchUserProfile = async () => {
            try {
                const response = await fetch('/api/user/profile');
                if (response.ok) {
                    const data = await response.json();
                    setSubscription(data.subscription);
                    setUsageStats(data.usageStats);
                    setRecentDiagnoses(data.recentDiagnoses);
                } else {
                    console.error('Failed to fetch user profile');
                    // Fallback to mock data if API fails
                    setSubscription({
                        plan: 'FREE',
                        status: 'ACTIVE',
                        dailyUploadLimit: 5,
                        chatRoundsLimit: 0,
                        historyDaysLimit: 0,
                        hasStructuredFindings: false,
                        hasPdfExport: false,
                        hasImageAnalysis: false,
                        hasTeachingTools: false,
                        hasBatchUpload: false
                    });
                    setUsageStats({
                        dailyUploads: 0,
                        monthlyUploads: 0,
                        totalDiagnoses: 0,
                        totalMessages: 0,
                        totalImages: 0,
                        pdfExports: 0,
                        teachingExports: 0
                    });
                    setRecentDiagnoses([]);
                }
            } catch (error) {
                console.error('Error fetching user profile:', error);
                // Fallback to mock data
                setSubscription({
                    plan: 'FREE',
                    status: 'ACTIVE',
                    dailyUploadLimit: 5,
                    chatRoundsLimit: 0,
                    historyDaysLimit: 0,
                    hasStructuredFindings: false,
                    hasPdfExport: false,
                    hasImageAnalysis: false,
                    hasTeachingTools: false,
                    hasBatchUpload: false
                });
                setUsageStats({
                    dailyUploads: 0,
                    monthlyUploads: 0,
                    totalDiagnoses: 0,
                    totalMessages: 0,
                    totalImages: 0,
                    pdfExports: 0,
                    teachingExports: 0
                });
                setRecentDiagnoses([]);
            } finally {
                setLoading(false);
            }
        };

        fetchUserProfile();
    }, []);

    const formatDate = (date: Date | string) => {
        return new Date(date).toLocaleDateString();
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'ACTIVE': return 'text-green-600 bg-green-100';
            case 'EXPIRED': return 'text-red-600 bg-red-100';
            case 'CANCELLED': return 'text-gray-600 bg-gray-100';
            default: return 'text-yellow-600 bg-yellow-100';
        }
    };

    const getRiskColor = (risk?: string) => {
        switch (risk) {
            case 'LOW': return 'text-green-600 bg-green-100';
            case 'MEDIUM': return 'text-yellow-600 bg-yellow-100';
            case 'HIGH': return 'text-red-600 bg-red-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50">
                <MedGemmaHeader />
                <div className="pt-32 pb-16">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center justify-center h-64">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                        </div>
                    </div>
                </div>
                <Footer />
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <MedGemmaHeader />
            
            {/* Hero Section */}
            <section className="pt-28 sm:pt-32 md:pt-36 pb-8 bg-gradient-to-b from-blue-100 to-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center">
                        <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-blue-800 mb-4">
                            {t('title')}
                        </h1>
                        <p className="text-lg md:text-xl text-blue-700 max-w-2xl mx-auto">
                            {t('subtitle')}
                        </p>
                    </div>
                </div>
            </section>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Left Column - Subscription & Features */}
                    <div className="lg:col-span-2 space-y-8">
                        {/* Subscription Card */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <h2 className="text-2xl font-bold text-gray-800 mb-6">{t('subscription.title')}</h2>
                            {subscription && (
                                <div className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <span className="text-gray-600">{t('subscription.plan')}</span>
                                        <span className="font-semibold text-blue-600">
                                            {t(`subscription.plans.${subscription.plan}`)}
                                        </span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-gray-600">{t('subscription.statusLabel')}</span>
                                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(subscription.status)}`}>
                                            {t(`subscription.status.${subscription.status}`)}
                                        </span>
                                    </div>
                                    {subscription.endDate && (
                                        <div className="flex items-center justify-between">
                                            <span className="text-gray-600">{t('subscription.validUntil')}</span>
                                            <span className="font-medium">{formatDate(subscription.endDate)}</span>
                                        </div>
                                    )}
                                    <div className="pt-4 border-t">
                                        <Link
                                            href={"/pricing" as Pathnames}
                                            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                        >
                                            {t('subscription.upgrade')}
                                        </Link>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Features Card */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
                            <div className="mb-4 sm:mb-6">
                                <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-2">{t('features.title')}</h2>
                                <p className="text-sm sm:text-base text-gray-600">{t('features.subtitle')}</p>
                            </div>
                            {subscription && (
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4">
                                    {/* Daily Upload Limit */}
                                    <div className="group p-3 sm:p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border border-blue-200 hover:shadow-md transition-all duration-200">
                                        <div className="flex items-start space-x-2 sm:space-x-3">
                                            <div className="flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                                                <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                                </svg>
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex items-center justify-between">
                                                    <h3 className="font-semibold text-gray-800 text-sm sm:text-base">{t('features.dailyUploads')}</h3>
                                                    <span className="font-bold text-blue-600 text-sm sm:text-base">
                                                        {subscription.dailyUploadLimit === -1 ? t('features.unlimited') : subscription.dailyUploadLimit}
                                                    </span>
                                                </div>
                                                <p className="text-xs sm:text-sm text-gray-600 mt-1">{t('features.dailyUploadsDesc')}</p>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Chat Rounds */}
                                    <div className="group p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200 hover:shadow-md transition-all duration-200">
                                        <div className="flex items-start space-x-3">
                                            <div className="flex-shrink-0 w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
                                                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                                </svg>
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex items-center justify-between">
                                                    <h3 className="font-semibold text-gray-800">{t('features.chatRounds')}</h3>
                                                    <span className={`font-bold ${subscription.chatRoundsLimit === 0 ? 'text-gray-400' : 'text-green-600'}`}>
                                                        {subscription.chatRoundsLimit === -1 ? t('features.unlimited') :
                                                         subscription.chatRoundsLimit === 0 ? t('features.disabled') :
                                                         `${subscription.chatRoundsLimit} ${t('features.rounds')}`}
                                                    </span>
                                                </div>
                                                <p className="text-sm text-gray-600 mt-1">{t('features.chatRoundsDesc')}</p>
                                            </div>
                                        </div>
                                    </div>

                                    {/* History Storage */}
                                    <div className="group p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg border border-purple-200 hover:shadow-md transition-all duration-200">
                                        <div className="flex items-start space-x-3">
                                            <div className="flex-shrink-0 w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
                                                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex items-center justify-between">
                                                    <h3 className="font-semibold text-gray-800">{t('features.historyDays')}</h3>
                                                    <span className={`font-bold ${subscription.historyDaysLimit === 0 ? 'text-gray-400' : 'text-purple-600'}`}>
                                                        {subscription.historyDaysLimit === -1 ? t('features.unlimited') :
                                                         subscription.historyDaysLimit === 0 ? t('features.disabled') :
                                                         `${subscription.historyDaysLimit} ${t('features.days')}`}
                                                    </span>
                                                </div>
                                                <p className="text-sm text-gray-600 mt-1">{t('features.historyDaysDesc')}</p>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Structured Findings */}
                                    <div className={`group p-4 rounded-lg border hover:shadow-md transition-all duration-200 ${
                                        subscription.hasStructuredFindings
                                            ? 'bg-gradient-to-br from-emerald-50 to-emerald-100 border-emerald-200'
                                            : 'bg-gradient-to-br from-gray-50 to-gray-100 border-gray-200'
                                    }`}>
                                        <div className="flex items-start space-x-3">
                                            <div className={`flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center ${
                                                subscription.hasStructuredFindings ? 'bg-emerald-600' : 'bg-gray-400'
                                            }`}>
                                                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                </svg>
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex items-center justify-between">
                                                    <h3 className="font-semibold text-gray-800">{t('features.structuredFindings')}</h3>
                                                    <span className={`font-bold ${subscription.hasStructuredFindings ? 'text-emerald-600' : 'text-gray-400'}`}>
                                                        {subscription.hasStructuredFindings ? t('features.enabled') : t('features.disabled')}
                                                    </span>
                                                </div>
                                                <p className="text-sm text-gray-600 mt-1">{t('features.structuredFindingsDesc')}</p>
                                            </div>
                                        </div>
                                    </div>

                                    {/* PDF Export */}
                                    <div className={`group p-4 rounded-lg border hover:shadow-md transition-all duration-200 ${
                                        subscription.hasPdfExport
                                            ? 'bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200'
                                            : 'bg-gradient-to-br from-gray-50 to-gray-100 border-gray-200'
                                    }`}>
                                        <div className="flex items-start space-x-3">
                                            <div className={`flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center ${
                                                subscription.hasPdfExport ? 'bg-orange-600' : 'bg-gray-400'
                                            }`}>
                                                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                </svg>
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex items-center justify-between">
                                                    <h3 className="font-semibold text-gray-800">{t('features.pdfExport')}</h3>
                                                    <span className={`font-bold ${subscription.hasPdfExport ? 'text-orange-600' : 'text-gray-400'}`}>
                                                        {subscription.hasPdfExport ? t('features.enabled') : t('features.disabled')}
                                                    </span>
                                                </div>
                                                <p className="text-sm text-gray-600 mt-1">{t('features.pdfExportDesc')}</p>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Image Analysis */}
                                    <div className={`group p-4 rounded-lg border hover:shadow-md transition-all duration-200 ${
                                        subscription.hasImageAnalysis
                                            ? 'bg-gradient-to-br from-indigo-50 to-indigo-100 border-indigo-200'
                                            : subscription.imageAnalysisComingSoon
                                            ? 'bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200'
                                            : 'bg-gradient-to-br from-gray-50 to-gray-100 border-gray-200'
                                    }`}>
                                        <div className="flex items-start space-x-3">
                                            <div className={`flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center ${
                                                subscription.hasImageAnalysis ? 'bg-indigo-600' :
                                                subscription.imageAnalysisComingSoon ? 'bg-yellow-600' : 'bg-gray-400'
                                            }`}>
                                                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex items-center justify-between">
                                                    <h3 className="font-semibold text-gray-800">{t('features.imageAnalysis')}</h3>
                                                    <span className={`font-bold ${
                                                        subscription.hasImageAnalysis ? 'text-indigo-600' :
                                                        subscription.imageAnalysisComingSoon ? 'text-yellow-600' : 'text-gray-400'
                                                    }`}>
                                                        {subscription.hasImageAnalysis ? t('features.enabled') :
                                                         subscription.imageAnalysisComingSoon ? t('features.comingSoon') : t('features.disabled')}
                                                    </span>
                                                </div>
                                                <p className="text-sm text-gray-600 mt-1">{t('features.imageAnalysisDesc')}</p>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Teaching Tools */}
                                    <div className={`group p-4 rounded-lg border hover:shadow-md transition-all duration-200 ${
                                        subscription.hasTeachingTools
                                            ? 'bg-gradient-to-br from-teal-50 to-teal-100 border-teal-200'
                                            : subscription.teachingToolsComingSoon
                                            ? 'bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200'
                                            : 'bg-gradient-to-br from-gray-50 to-gray-100 border-gray-200'
                                    }`}>
                                        <div className="flex items-start space-x-3">
                                            <div className={`flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center ${
                                                subscription.hasTeachingTools ? 'bg-teal-600' :
                                                subscription.teachingToolsComingSoon ? 'bg-yellow-600' : 'bg-gray-400'
                                            }`}>
                                                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                                </svg>
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex items-center justify-between">
                                                    <h3 className="font-semibold text-gray-800">{t('features.teachingTools')}</h3>
                                                    <span className={`font-bold ${
                                                        subscription.hasTeachingTools ? 'text-teal-600' :
                                                        subscription.teachingToolsComingSoon ? 'text-yellow-600' : 'text-gray-400'
                                                    }`}>
                                                        {subscription.hasTeachingTools ? t('features.enabled') :
                                                         subscription.teachingToolsComingSoon ? t('features.comingSoon') : t('features.disabled')}
                                                    </span>
                                                </div>
                                                <p className="text-sm text-gray-600 mt-1">{t('features.teachingToolsDesc')}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Upgrade CTA */}
                            {subscription && subscription.plan === 'FREE' && (
                                <div className="mt-6 p-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg text-white">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <h3 className="font-semibold">{t('features.upgradeToUnlock')}</h3>
                                            <p className="text-blue-100 text-sm mt-1">{t('features.upgradeDesc')}</p>
                                        </div>
                                        <Link
                                            href={"/pricing" as Pathnames}
                                            className="px-4 py-2 bg-white text-blue-600 rounded-lg hover:bg-blue-50 transition-colors font-medium"
                                        >
                                            {t('subscription.upgrade')}
                                        </Link>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Right Column - Usage Stats & Recent Diagnoses */}
                    <div className="space-y-8">
                        {/* Usage Stats Card */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <h2 className="text-xl font-bold text-gray-800 mb-6">{t('usage.title')}</h2>
                            {usageStats && (
                                <div className="space-y-4">
                                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                                        <div className="text-2xl font-bold text-blue-600">{usageStats.dailyUploads}</div>
                                        <div className="text-sm text-blue-700">{t('usage.today')}</div>
                                    </div>
                                    <div className="grid grid-cols-2 gap-4 text-center">
                                        <div className="p-3 bg-gray-50 rounded-lg">
                                            <div className="text-lg font-semibold">{usageStats.totalDiagnoses}</div>
                                            <div className="text-xs text-gray-600">{t('usage.diagnoses')}</div>
                                        </div>
                                        <div className="p-3 bg-gray-50 rounded-lg">
                                            <div className="text-lg font-semibold">{usageStats.totalMessages}</div>
                                            <div className="text-xs text-gray-600">{t('usage.messages')}</div>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Recent Diagnoses Card */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <div className="flex items-center justify-between mb-6">
                                <h2 className="text-xl font-bold text-gray-800">{t('recentDiagnoses.title')}</h2>
                                <Link
                                    href={"/ai-medical-diagnosis" as Pathnames}
                                    className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                                >
                                    {t('recentDiagnoses.viewAll')}
                                </Link>
                            </div>
                            {recentDiagnoses.length > 0 ? (
                                <div className="space-y-3">
                                    {recentDiagnoses.map((diagnosis) => (
                                        <div key={diagnosis.id} className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                            <div className="flex items-center justify-between mb-2">
                                                <h3 className="font-medium text-gray-800 truncate">{diagnosis.title}</h3>
                                                {diagnosis.riskLevel && (
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(diagnosis.riskLevel)}`}>
                                                        {t(`recentDiagnoses.riskLevel.${diagnosis.riskLevel}`)}
                                                    </span>
                                                )}
                                            </div>
                                            <div className="flex items-center justify-between text-sm text-gray-500">
                                                <span>{t(`recentDiagnoses.type.${diagnosis.type}`)}</span>
                                                <span>{formatDate(diagnosis.timestamp)}</span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8">
                                    <div className="text-gray-400 mb-2">{t('recentDiagnoses.noRecords')}</div>
                                    <Link
                                        href={"/ai-medical-diagnosis" as Pathnames}
                                        className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                                    >
                                        {t('recentDiagnoses.startFirst')}
                                    </Link>
                                </div>
                            )}
                        </div>

                        {/* Quick Actions */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <div className="space-y-3">
                                <Link
                                    href={"/ai-medical-diagnosis" as Pathnames}
                                    className="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                >
                                    {t('actions.newDiagnosis')}
                                </Link>
                                <Link
                                    href={"/pricing" as Pathnames}
                                    className="w-full inline-flex items-center justify-center px-4 py-2 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
                                >
                                    {t('subscription.upgrade')}
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Settings Section */}
                <div className="mt-8 sm:mt-12 bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
                    <div className="mb-4 sm:mb-6">
                        <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-2">{t('settings.title')}</h2>
                        <p className="text-sm sm:text-base text-gray-600">{t('settings.subtitle')}</p>
                    </div>

                    <div className="space-y-6 sm:space-y-8">
                        {/* Preferences */}
                        <div>
                            <h3 className="text-base sm:text-lg font-semibold text-gray-800 mb-3 sm:mb-4 flex items-center">
                                <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                {t('settings.preferences.title')}
                            </h3>

                            <div className="space-y-3 sm:space-y-4">
                                <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                                        <div className="flex-1">
                                            <h4 className="font-medium text-gray-800 text-sm sm:text-base">{t('settings.preferences.emailNotifications')}</h4>
                                            <p className="text-xs sm:text-sm text-gray-600 mt-1">{t('settings.preferences.emailNotificationsDesc')}</p>
                                        </div>
                                        <label className="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" className="sr-only peer" defaultChecked />
                                            <div className="w-9 h-5 sm:w-11 sm:h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 sm:after:h-5 sm:after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <Footer />
        </div>
    );
}

import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getBaseUrl } from '@/utils/metadata';
import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/utils/auth';
import ProfileClient from './client';

type Props = {
    params: Promise<{ locale: string }>;
};

export async function generateMetadata({
    params
}: Props): Promise<Metadata> {
    const resolvedParams = await params;
    const locale = resolvedParams.locale;

    const t = await getTranslations('pages.profile.metadata');
    const baseUrl = await getBaseUrl();

    return {
        title: t('title'),
        description: t('description'),
        openGraph: {
            title: t('title'),
            description: t('description'),
            type: 'website',
            url: `${baseUrl}${locale === 'en' ? '' : `/${locale}`}/profile`,
        }
    };
}

export default async function ProfilePage() {
    const session = await getServerSession(authOptions);
    
    if (!session) {
        redirect('/api/auth/signin');
    }

    return <ProfileClient />;
}

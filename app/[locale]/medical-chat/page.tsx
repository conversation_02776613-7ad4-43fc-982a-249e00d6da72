'use client';

import { useState, useRef, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import MedGemmaHeader from '@/components/MedGemmaHeader';
import ChatMessage from '@/components/ChatMessage';
import ImageUpload from '@/components/ImageUpload';
import Image from 'next/image';
import Footer from '@/components/ui/Footer';

interface ChatMessageData {
    id: string;
    type: 'user' | 'assistant';
    content: string;
    image?: string;
    timestamp: Date;
    isTyping?: boolean;
}

interface MedGeminiResponse {
    response: string;
    model: string;
    usage: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}

export default function MedicalChatPage() {
    const t = useTranslations('pages.medicalChat');
    const [messages, setMessages] = useState<ChatMessageData[]>([]);
    const [inputText, setInputText] = useState('');
    const [selectedImage, setSelectedImage] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [model, setModel] = useState<'gpt-4o' | 'gemini-2.5-flash' | 'mistral-small-3.2' | 'kimi-k2' | 'hunyuan-a13b' | 'deepseek-r1t2-chimera'>('mistral-small-3.2');

    // 模拟用户状态 - 在实际应用中这些应该来自认证系统
    const [userStatus, setUserStatus] = useState({
        isLoggedIn: false,
        isPremium: false,
        remainingQuota: 10,
        totalQuota: 20
    });

    // 模拟登录功能
    const handleLogin = () => {
        setUserStatus(prev => ({
            ...prev,
            isLoggedIn: true,
            remainingQuota: 20
        }));
    };

    // 模拟升级功能
    const handleUpgrade = () => {
        setUserStatus(prev => ({
            ...prev,
            isPremium: true
        }));
    };
    
    const fileInputRef = useRef<HTMLInputElement>(null);
    const messagesEndRef = useRef<HTMLDivElement>(null);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    const handleImageSelect = (file: File, preview: string) => {
        setSelectedImage(file);
        setImagePreview(preview);
    };

    const removeImage = () => {
        setSelectedImage(null);
        setImagePreview(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!inputText.trim() && !selectedImage) return;

        // 检查免费用户的额度
        if (!userStatus.isPremium && userStatus.remainingQuota <= 0) {
            alert('您的免费额度已用完，请升级到高级版或登录获取更多额度');
            return;
        }

        const userMessage: ChatMessageData = {
            id: Date.now().toString(),
            type: 'user',
            content: inputText,
            image: imagePreview || undefined,
            timestamp: new Date()
        };

        setMessages(prev => [...prev, userMessage]);
        setIsLoading(true);

        try {
            const formData = new FormData();
            formData.append('prompt', inputText);
            formData.append('model', model);

            if (selectedImage) {
                formData.append('image', selectedImage);
            }

            const response = await fetch('/api/openrouter-chat', {
                method: 'POST',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: MedGeminiResponse = await response.json();

            const assistantMessage: ChatMessageData = {
                id: (Date.now() + 1).toString(),
                type: 'assistant',
                content: data.response,
                timestamp: new Date(),
                isTyping: true
            };

            setMessages(prev => [...prev, assistantMessage]);

            // 减少免费用户的额度
            if (!userStatus.isPremium && userStatus.remainingQuota > 0) {
                setUserStatus(prev => ({
                    ...prev,
                    remainingQuota: prev.remainingQuota - 1
                }));
            }
        } catch (error) {
            console.error('Error:', error);
            const errorMessage: ChatMessageData = {
                id: (Date.now() + 1).toString(),
                type: 'assistant',
                content: 'Sorry, I encountered an error while processing your request. Please try again.',
                timestamp: new Date()
            };
            setMessages(prev => [...prev, errorMessage]);
        } finally {
            setIsLoading(false);
            setInputText('');
            removeImage();
        }
    };

    return (
        <div className="min-h-screen bg-gray-50">
            <MedGemmaHeader />
            
            {/* Hero Section */}
            <section className="pt-20 sm:pt-24 md:pt-28 pb-6 sm:pb-8 bg-gradient-to-b from-blue-100 to-gray-50">
                <div className="max-w-4xl mx-auto px-4 text-center">
                    <div className="inline-flex items-center px-3 py-1.5 sm:px-4 sm:py-2 bg-blue-100 text-blue-800 rounded-full text-xs sm:text-sm font-medium mb-3 sm:mb-4">
                        <svg className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                        </svg>
                        {t('content.hero.badge')}
                    </div>
                    <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-blue-800 mb-3 sm:mb-4">
                        {t('content.hero.title')}
                    </h1>
                    <p className="text-base sm:text-lg md:text-xl text-blue-700 max-w-2xl mx-auto px-2">
                        {t('content.hero.description')}
                    </p>
                </div>
            </section>

            {/* Chat Interface */}
            <div className="max-w-4xl mx-auto px-2 sm:px-4 pb-4 sm:pb-8">
                <div className="bg-white rounded-xl sm:rounded-2xl shadow-lg border border-blue-100 overflow-hidden">
                    {/* Enhanced Header with User Status */}
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100 p-4 sm:p-5">
                        {/* Top Row - Status and User Info */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-4">
                            <div className="flex items-center space-x-3">
                                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse shadow-sm"></div>
                                <span className="text-sm font-semibold text-blue-800">{t('content.chat.status')}</span>
                            </div>

                            {/* User Status Section */}
                            <div className="flex items-center space-x-4">
                                {/* Login Status */}
                                <div className="flex items-center space-x-2">
                                    <div className={`w-2 h-2 rounded-full ${userStatus.isLoggedIn ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                                    <span className="text-xs font-medium text-gray-600">
                                        {userStatus.isLoggedIn ? t('content.chat.userStatus.loggedIn') : t('content.chat.userStatus.notLoggedIn')}
                                    </span>
                                </div>

                                {/* User Type & Quota */}
                                {userStatus.isLoggedIn ? (
                                    <div className="flex items-center space-x-3">
                                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                                            userStatus.isPremium
                                                ? 'bg-yellow-100 text-yellow-800 border border-yellow-200'
                                                : 'bg-gray-100 text-gray-600 border border-gray-200'
                                        }`}>
                                            {userStatus.isPremium ? t('content.chat.userStatus.premiumUser') : t('content.chat.userStatus.freeUser')}
                                        </span>

                                        <span className="text-xs text-gray-600">
                                            {userStatus.isPremium
                                                ? t('content.chat.userStatus.unlimitedQuota')
                                                : t('content.chat.userStatus.remainingQuota', { count: userStatus.remainingQuota })
                                            }
                                        </span>

                                        {!userStatus.isPremium && (
                                            <button
                                                onClick={handleUpgrade}
                                                className="text-xs bg-gradient-to-r from-blue-600 to-blue-700 text-white px-3 py-1 rounded-full hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-sm hover:shadow-md"
                                            >
                                                {t('content.chat.userStatus.upgradeButton')}
                                            </button>
                                        )}
                                    </div>
                                ) : (
                                    <div className="flex items-center space-x-2">
                                        <span className="text-xs text-gray-500">{t('content.chat.userStatus.loginPrompt')}</span>
                                        <button
                                            onClick={handleLogin}
                                            className="text-xs bg-blue-600 text-white px-3 py-1 rounded-full hover:bg-blue-700 transition-colors duration-200"
                                        >
                                            {t('content.chat.userStatus.loginButton')}
                                        </button>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Bottom Row - Model Selection */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                            <div className="flex items-center space-x-2">
                                <label className="text-xs font-medium text-blue-700">Model:</label>
                                <span className="text-xs text-gray-500">选择AI模型</span>
                            </div>
                            <div className="flex items-center space-x-3">
                                <select
                                    value={model}
                                    onChange={(e) => setModel(e.target.value as typeof model)}
                                    className="px-3 py-2 text-sm border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white shadow-sm hover:border-blue-300 transition-colors min-w-[200px] appearance-none bg-no-repeat bg-right pr-8"
                                    style={{
                                        backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`,
                                        backgroundPosition: 'right 0.5rem center',
                                        backgroundSize: '1.5em 1.5em'
                                    }}
                                >
                                    <option value="gpt-4o">GPT-4o (Premium) 🔥</option>
                                    <option value="gemini-2.5-flash">Gemini 2.5 Flash 💎</option>
                                    <option value="mistral-small-3.2">Mistral Small 3.2 (Free) ✅📷</option>
                                    <option value="kimi-k2">Kimi K2 (Free) ✅</option>
                                    <option value="hunyuan-a13b">Hunyuan A13B (Free) ✅</option>
                                    <option value="deepseek-r1t2-chimera">DeepSeek R1T2 Chimera (Free) ✅</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    {/* Messages Area */}
                    <div className="h-80 sm:h-96 md:h-[500px] overflow-y-auto p-4 sm:p-6 bg-gray-50">
                        {messages.length === 0 && (
                            <div className="text-center py-12 sm:py-16">
                                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 shadow-sm">
                                    <svg className="w-8 h-8 sm:w-10 sm:h-10 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <h3 className="text-lg sm:text-xl font-bold text-gray-800 mb-3">{t('content.chat.emptyState.title')}</h3>
                                <p className="text-gray-600 text-sm sm:text-base px-4 max-w-md mx-auto leading-relaxed">{t('content.chat.emptyState.description')}</p>
                            </div>
                        )}
                        
                        {messages.map((message) => (
                            <ChatMessage
                                key={message.id}
                                id={message.id}
                                type={message.type}
                                content={message.content}
                                image={message.image}
                                timestamp={message.timestamp}
                                isTyping={message.isTyping}
                            />
                        ))}
                        
                        {isLoading && (
                            <div className="flex justify-start">
                                <div className="bg-gray-100 rounded-2xl px-4 py-3 max-w-xs">
                                    <div className="flex space-x-1">
                                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                                    </div>
                                </div>
                            </div>
                        )}
                        <div ref={messagesEndRef} />
                    </div>

                    {/* Input Area */}
                    <div className="border-t border-gray-200 bg-white p-4 sm:p-6">
                        {/* Image Upload Section */}
                        {!isLoading && (
                            <div className="mb-4">
                                <ImageUpload
                                    onImageSelect={handleImageSelect}
                                    onImageRemove={removeImage}
                                    imagePreview={imagePreview}
                                    disabled={isLoading}
                                />
                            </div>
                        )}

                        {/* Input Form */}
                        <form onSubmit={handleSubmit} className="space-y-3">
                            <div className="flex items-start space-x-3">
                                <div className="flex-1">
                                    <textarea
                                        value={inputText}
                                        onChange={(e) => setInputText(e.target.value)}
                                        placeholder={t('content.chat.input.placeholder')}
                                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm sm:text-base shadow-sm hover:border-gray-400 transition-colors"
                                        rows={1}
                                        style={{ minHeight: '48px', maxHeight: '120px' }}
                                    />
                                </div>

                                <button
                                    type="submit"
                                    disabled={isLoading || (!inputText.trim() && !selectedImage)}
                                    className="h-12 w-12 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex-shrink-0 shadow-sm hover:shadow-md flex items-center justify-center mt-0"
                                >
                                    {isLoading ? (
                                        <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    ) : (
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                        </svg>
                                    )}
                                </button>
                            </div>

                            {/* Disclaimer */}
                            <p className="text-xs text-gray-500 text-center px-2 leading-relaxed">
                                {t('content.chat.disclaimer')}
                            </p>
                        </form>
                    </div>
                </div>
            </div>

            {/* Footer */}
            <Footer />
        </div>
    );
}

'use client';

import { useState, useRef, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import MedGemmaHeader from '@/components/MedGemmaHeader';
import ChatMessage from '@/components/ChatMessage';
import ImageUpload from '@/components/ImageUpload';
import Image from 'next/image';

interface ChatMessageData {
    id: string;
    type: 'user' | 'assistant';
    content: string;
    image?: string;
    timestamp: Date;
    isTyping?: boolean;
}

interface MedGeminiResponse {
    response: string;
    model: string;
    usage: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}

export default function MedicalChatPage() {
    const t = useTranslations('pages.medicalChat');
    const [messages, setMessages] = useState<ChatMessageData[]>([]);
    const [inputText, setInputText] = useState('');
    const [selectedImage, setSelectedImage] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [model, setModel] = useState<'gemini-1.5-pro' | 'gemini-1.5-flash' | 'gemini-2.0-flash'>('gemini-1.5-flash');
    
    const fileInputRef = useRef<HTMLInputElement>(null);
    const messagesEndRef = useRef<HTMLDivElement>(null);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    const handleImageSelect = (file: File, preview: string) => {
        setSelectedImage(file);
        setImagePreview(preview);
    };

    const removeImage = () => {
        setSelectedImage(null);
        setImagePreview(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!inputText.trim() && !selectedImage) return;

        const userMessage: ChatMessageData = {
            id: Date.now().toString(),
            type: 'user',
            content: inputText,
            image: imagePreview || undefined,
            timestamp: new Date()
        };

        setMessages(prev => [...prev, userMessage]);
        setIsLoading(true);

        try {
            const formData = new FormData();
            formData.append('prompt', inputText);
            formData.append('model', model);
            
            if (selectedImage) {
                formData.append('image', selectedImage);
            }

            const response = await fetch('/api/medgemma', {
                method: 'POST',
                body: formData,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: MedGeminiResponse = await response.json();

            const assistantMessage: ChatMessageData = {
                id: (Date.now() + 1).toString(),
                type: 'assistant',
                content: data.response,
                timestamp: new Date(),
                isTyping: true
            };

            setMessages(prev => [...prev, assistantMessage]);
        } catch (error) {
            console.error('Error:', error);
            const errorMessage: ChatMessageData = {
                id: (Date.now() + 1).toString(),
                type: 'assistant',
                content: 'Sorry, I encountered an error while processing your request. Please try again.',
                timestamp: new Date()
            };
            setMessages(prev => [...prev, errorMessage]);
        } finally {
            setIsLoading(false);
            setInputText('');
            removeImage();
        }
    };

    return (
        <div className="min-h-screen bg-gray-50">
            <MedGemmaHeader />
            
            {/* Hero Section */}
            <section className="pt-20 sm:pt-24 md:pt-28 pb-6 sm:pb-8 bg-gradient-to-b from-blue-100 to-gray-50">
                <div className="max-w-4xl mx-auto px-4 text-center">
                    <div className="inline-flex items-center px-3 py-1.5 sm:px-4 sm:py-2 bg-blue-100 text-blue-800 rounded-full text-xs sm:text-sm font-medium mb-3 sm:mb-4">
                        <svg className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                        </svg>
                        Medical AI Assistant
                    </div>
                    <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-blue-800 mb-3 sm:mb-4">
                        Medical Consultation Chat
                    </h1>
                    <p className="text-base sm:text-lg md:text-xl text-blue-700 max-w-2xl mx-auto px-2">
                        Get medical insights with text and image analysis powered by Google's Med Gemini
                    </p>
                </div>
            </section>

            {/* Chat Interface */}
            <div className="max-w-4xl mx-auto px-2 sm:px-4 pb-4 sm:pb-8">
                <div className="bg-white rounded-xl sm:rounded-2xl shadow-lg border border-blue-100 overflow-hidden">
                    {/* Model Selection Header */}
                    <div className="bg-blue-50 border-b border-blue-100 p-3 sm:p-4">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
                            <div className="flex items-center space-x-2">
                                <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-green-500 rounded-full animate-pulse"></div>
                                <span className="text-xs sm:text-sm font-medium text-blue-800">Med Gemini Active</span>
                            </div>
                            <select
                                value={model}
                                onChange={(e) => setModel(e.target.value as typeof model)}
                                className="px-2 py-1.5 sm:px-3 sm:py-2 text-xs sm:text-sm border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white"
                            >
                                <option value="gemini-1.5-flash">Gemini 1.5 Flash (Recommended)</option>
                                <option value="gemini-1.5-pro">Gemini 1.5 Pro (High Performance)</option>
                                <option value="gemini-2.0-flash">Gemini 2.0 Flash (Latest)</option>
                            </select>
                        </div>
                    </div>

                    {/* Messages Area */}
                    <div className="h-80 sm:h-96 md:h-[500px] overflow-y-auto p-3 sm:p-4">
                        {messages.length === 0 && (
                            <div className="text-center py-8 sm:py-12">
                                <div className="w-12 h-12 sm:w-16 sm:h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                                    <svg className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <h3 className="text-base sm:text-lg font-semibold text-gray-800 mb-2">Start a Medical Consultation</h3>
                                <p className="text-gray-600 text-xs sm:text-sm px-4">Ask questions about symptoms, upload medical images, or seek health advice</p>
                            </div>
                        )}
                        
                        {messages.map((message) => (
                            <ChatMessage
                                key={message.id}
                                id={message.id}
                                type={message.type}
                                content={message.content}
                                image={message.image}
                                timestamp={message.timestamp}
                                isTyping={message.isTyping}
                            />
                        ))}
                        
                        {isLoading && (
                            <div className="flex justify-start">
                                <div className="bg-gray-100 rounded-2xl px-4 py-3 max-w-xs">
                                    <div className="flex space-x-1">
                                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                                    </div>
                                </div>
                            </div>
                        )}
                        <div ref={messagesEndRef} />
                    </div>

                    {/* Input Area */}
                    <div className="border-t border-gray-200 p-3 sm:p-4">
                        {(imagePreview || !isLoading) && (
                            <div className="mb-3 sm:mb-4">
                                <ImageUpload
                                    onImageSelect={handleImageSelect}
                                    onImageRemove={removeImage}
                                    imagePreview={imagePreview}
                                    disabled={isLoading}
                                />
                            </div>
                        )}
                        
                        <form onSubmit={handleSubmit} className="flex items-end space-x-2">
                            <div className="flex-1">
                                <textarea
                                    value={inputText}
                                    onChange={(e) => setInputText(e.target.value)}
                                    placeholder="Describe your symptoms or ask a medical question..."
                                    className="w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none text-sm sm:text-base"
                                    rows={1}
                                    style={{ minHeight: '40px', maxHeight: '120px' }}
                                />
                            </div>
                            

                            
                            <button
                                type="submit"
                                disabled={isLoading || (!inputText.trim() && !selectedImage)}
                                className="p-2.5 sm:p-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex-shrink-0"
                            >
                                <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                </svg>
                            </button>
                        </form>
                        
                        <p className="text-xs text-gray-500 mt-2 text-center px-2">
                            This is for educational purposes only. Always consult healthcare professionals for medical advice.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
}

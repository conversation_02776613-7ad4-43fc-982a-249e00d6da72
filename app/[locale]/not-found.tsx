'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

export default function LocaleNotFound() {
    const router = useRouter();
    const [countdown, setCountdown] = useState(6);
    const t = useTranslations('pages.notFound');

    useEffect(() => {
        const timer = setInterval(() => {
            setCountdown((prev) => prev - 1);
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    useEffect(() => {
        if (countdown === 0) {
            const redirect = setTimeout(() => {
                router.push('/');
            }, 100);

            return () => clearTimeout(redirect);
        }
    }, [countdown, router]);

    return (
        <main className="flex-1 flex items-center justify-center min-h-[calc(100vh-8rem)]">
            <div className="text-center space-y-8">
                <h1 className="text-9xl font-bold text-gradient">{t('title')}</h1>
                <h2 className="text-2xl text-primary-dark">{t('subtitle')}</h2>
                <p className="text-gray-600">
                    {t('message')}
                </p>
                <p className="text-gray-500">
                    {t('redirectMessage', { countdown })}
                </p>
                <button
                    onClick={() => router.push('/')}
                    className="px-6 py-3 bg-primary/10 text-primary rounded border border-primary/20 
                             hover:bg-primary/20 transition-colors"
                >
                    {t('buttonText')}
                </button>
            </div>
        </main>
    );
} 
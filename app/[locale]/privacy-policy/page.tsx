import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { useTranslations } from 'next-intl';
import { getBaseUrl } from '@/utils/metadata';

type Props = {
    params: Promise<{ locale: string }>;
};

export async function generateMetadata({
    params
}: Props): Promise<Metadata> {
    const resolvedParams = await params;
    const locale = resolvedParams.locale;

    const t = await getTranslations('pages.privacy.metadata');
    const baseUrl = await getBaseUrl();

    return {
        title: t('title'),
        description: t('description'),
        openGraph: {
            title: t('title'),
            description: t('description'),
            type: 'website',
            url: `${baseUrl}${locale === 'en' ? '' : `/${locale}`}/privacy-policy`,
        }
    };
}

export default function PrivacyPolicy() {
    const t = useTranslations('pages.privacy.content');
    const sectionKeys = ['dataCollection', 'dataUsage', 'userRights', 'medicalDisclaimer', 'compliance', 'dataRetention'];

    return (
        <div className="min-h-[calc(100vh-8rem)] pt-16 sm:pt-20">
            <div className="container mx-auto px-4 py-6 sm:py-8 lg:py-12">
                <div className="max-w-4xl mx-auto">
                    <div className="text-center space-y-4 sm:space-y-6 mb-8 sm:mb-12 lg:mb-16">
                        <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight text-gradient">
                            {t('title')}
                        </h1>
                        <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-3xl mx-auto">
                            {t('description')}
                        </p>
                    </div>

                    <div className="space-y-6 sm:space-y-8">
                        {sectionKeys.map((key) => (
                            <section key={key}
                                className="bg-white rounded-xl p-4 sm:p-6 lg:p-8 shadow-sm
                                         border border-gray-100 hover:shadow-md transition-shadow">
                                <h2 className="text-lg sm:text-xl lg:text-2xl font-semibold mb-3 sm:mb-4 text-primary">
                                    {t(`sections.${key}.title`)}
                                </h2>
                                <div className="space-y-3 sm:space-y-4">
                                    <p className="text-gray-600 text-sm sm:text-base leading-relaxed">
                                        {t(`sections.${key}.paragraph1`)}
                                    </p>
                                    <p className="text-gray-600 text-sm sm:text-base leading-relaxed">
                                        {t(`sections.${key}.paragraph2`)}
                                    </p>
                                </div>
                            </section>
                        ))}
                    </div>

                    <p className="text-xs sm:text-sm text-gray-500 text-center mt-6 sm:mt-8">
                        {t('lastUpdated')}
                    </p>
                </div>
            </div>
        </div>
    );
}
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { useTranslations } from 'next-intl';
import { getBaseUrl } from '@/utils/metadata';

type Props = {
    params: Promise<{ locale: string }>;
};

export async function generateMetadata({
    params
}: Props): Promise<Metadata> {
    const resolvedParams = await params;
    const locale = resolvedParams.locale;

    const t = await getTranslations('pages.privacy.metadata');
    const baseUrl = await getBaseUrl();

    return {
        title: t('title'),
        description: t('description'),
        openGraph: {
            title: t('title'),
            description: t('description'),
            type: 'website',
            url: `${baseUrl}${locale === 'en' ? '' : `/${locale}`}/privacy-policy`,
        }
    };
}

export default function PrivacyPolicy() {
    const t = useTranslations('pages.privacy.content');
    const sectionKeys = ['dataCollection', 'dataUsage', 'userRights', 'medicalDisclaimer', 'compliance', 'dataRetention'];

    return (
        <div className="flex items-center justify-center min-h-[calc(100vh-8rem)] pt-16 sm:pt-20">
            <div className="container mx-auto px-4 py-8 sm:py-12">
                <div className="max-w-4xl mx-auto">
                    <div className="text-center space-y-6 mb-16">
                        <h1 className="text-4xl sm:text-5xl font-bold tracking-tight text-gradient">
                            {t('title')}
                        </h1>
                        <p className="text-lg sm:text-xl text-gray-600">
                            {t('description')}
                        </p>
                    </div>

                    <div className="space-y-8">
                        {sectionKeys.map((key) => (
                            <section key={key}
                                className="bg-white rounded-xl p-6 sm:p-8 shadow-sm 
                                         border border-gray-100">
                                <h2 className="text-xl sm:text-2xl font-semibold mb-4 text-primary">
                                    {t(`sections.${key}.title`)}
                                </h2>
                                <div className="space-y-4">
                                    <p className="text-gray-600">
                                        {t(`sections.${key}.paragraph1`)}
                                    </p>
                                    <p className="text-gray-600">
                                        {t(`sections.${key}.paragraph2`)}
                                    </p>
                                </div>
                            </section>
                        ))}
                    </div>

                    <p className="text-sm text-gray-500 text-center mt-8">
                        {t('lastUpdated')}
                    </p>
                </div>
            </div>
        </div>
    );
}
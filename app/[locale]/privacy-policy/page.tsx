import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { useTranslations } from 'next-intl';
import { getBaseUrl } from '@/utils/metadata';
import MedGemmaHeader from '@/components/MedGemmaHeader';
import Image from 'next/image';
import Footer from '@/components/ui/Footer';

type Props = {
    params: Promise<{ locale: string }>;
};

export async function generateMetadata({
    params
}: Props): Promise<Metadata> {
    const resolvedParams = await params;
    const locale = resolvedParams.locale;

    const t = await getTranslations('pages.privacy.metadata');
    const baseUrl = await getBaseUrl();

    return {
        title: t('title'),
        description: t('description'),
        openGraph: {
            title: t('title'),
            description: t('description'),
            type: 'website',
            url: `${baseUrl}${locale === 'en' ? '' : `/${locale}`}/privacy-policy`,
        }
    };
}

export default function PrivacyPolicy() {
    const t = useTranslations('pages.privacy.content');
    const sectionKeys = ['dataCollection', 'dataUsage', 'userRights', 'medicalDisclaimer', 'compliance', 'dataRetention'];

    return (
        <div className="min-h-screen bg-white">
            {/* MedGemma Header */}
            <MedGemmaHeader />

            {/* Hero Section */}
            <section className="pt-32 md:pt-36 pb-12 md:pb-16 bg-gradient-to-b from-blue-100 to-white relative overflow-hidden">
                {/* Background Image */}
                <div className="absolute inset-0 z-0">
                    <Image
                        src="/bg.png"
                        alt="Medical AI Background"
                        fill
                        className="object-cover opacity-5"
                        priority
                    />
                </div>

                {/* Content */}
                <div className="max-w-screen-lg mx-auto px-4 relative z-10">
                    <div className="text-center space-y-4 sm:space-y-6">
                        <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-4">
                            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            Legal Document
                        </div>
                        <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-blue-800 mb-4">
                            {t('title')}
                        </h1>
                        <p className="text-lg sm:text-xl md:text-2xl text-blue-700 max-w-3xl mx-auto leading-relaxed">
                            {t('description')}
                        </p>
                    </div>
                </div>
            </section>

            {/* Content Section */}
            <section className="py-12 md:py-16">
                <div className="max-w-screen-lg mx-auto px-4">
                    <div className="space-y-8 md:space-y-12">
                        {sectionKeys.map((key, index) => (
                            <div key={key}
                                className="bg-white rounded-2xl p-6 sm:p-8 md:p-10 shadow-lg border border-blue-100 hover:shadow-xl transition-all duration-300 hover:border-blue-200">
                                <div className="flex items-start space-x-4 mb-6">
                                    <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <span className="text-blue-600 font-bold text-lg">{index + 1}</span>
                                    </div>
                                    <div className="flex-1">
                                        <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-blue-800 mb-4">
                                            {t(`sections.${key}.title`)}
                                        </h2>
                                    </div>
                                </div>
                                <div className="space-y-4 sm:space-y-6 ml-14">
                                    <p className="text-gray-700 text-sm sm:text-base md:text-lg leading-relaxed">
                                        {t(`sections.${key}.paragraph1`)}
                                    </p>
                                    <p className="text-gray-700 text-sm sm:text-base md:text-lg leading-relaxed">
                                        {t(`sections.${key}.paragraph2`)}
                                    </p>
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* Last Updated */}
                    <div className="mt-12 md:mt-16 text-center">
                        <div className="inline-flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-full text-sm">
                            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                            {t('lastUpdated')}
                        </div>
                    </div>
                </div>
            </section>

            {/* Footer */}
            <Footer />
        </div>
    );
}
version: '3.8'

services:
  medgemma:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        APP_ENV: development
    container_name: medgemma
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: development
    env_file:
      - .env
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - medgemma_db
    restart: unless-stopped

  medgemma_db:
    image: postgres:alpine
    ports:
      - "5432:5432"
    env_file:
      - .env
    volumes:
      - postgres_data:/var/lib/postgresql/data
    container_name: medgemma_db
    restart: unless-stopped

volumes:
  postgres_data:

# Google Authentication Testing Guide

## 测试步骤

### 1. 启动开发服务器
```bash
npm run dev
```

### 2. 访问应用
打开浏览器访问 `http://localhost:3000`

### 3. 测试登录功能
1. 点击页面右上角的 "Sign in with Google" 按钮
2. 应该会跳转到Google OAuth页面
3. 选择Google账户并授权
4. 成功后应该返回到应用，显示用户头像和姓名
5. 点击 "Sign Out" 按钮应该能够登出

### 4. 移动端测试
1. 在移动设备或浏览器开发者工具的移动模式下测试
2. 点击右上角的菜单按钮
3. 在移动菜单中找到认证区域
4. 测试登录和登出功能

## 预期行为

### 登录前
- 显示蓝色边框的 "Sign in with Google" 按钮
- 按钮有Google图标和文字
- 悬停时有蓝色背景渐变效果

### 登录后
- 显示用户头像（圆形，带蓝色边框）
- 显示用户姓名（蓝色文字）
- 显示蓝色的 "Sign Out" 按钮

### 响应式设计
- 桌面端：按钮在header右侧显示
- 移动端：按钮在移动菜单的认证区域显示
- 不同屏幕尺寸下文字和图标大小适配

## 故障排除

### 如果遇到404错误
1. 检查 `.env` 文件中的环境变量是否正确设置
2. 确保 `NEXT_PUBLIC_GOOGLE_CLIENT_ID` 和 `GOOGLE_CLIENT_SECRET` 已配置
3. 验证Google OAuth设置中的重定向URI是否正确

### 如果遇到数据库错误
1. 确保PostgreSQL服务正在运行
2. 运行 `npx prisma db push` 确保数据库schema是最新的
3. 检查 `DATABASE_URL` 是否正确

### 如果样式不正确
1. 确保Tailwind CSS正常工作
2. 检查浏览器控制台是否有CSS错误
3. 验证响应式断点是否正确

## 设计特点

### 视觉风格
- 使用MedGemma的蓝色主题（blue-600, blue-700, blue-800）
- 保持Google品牌色彩的图标
- 现代化的圆角和阴影效果
- 平滑的过渡动画

### 用户体验
- 清晰的视觉反馈
- 响应式设计适配所有设备
- 无障碍访问支持
- 快速的加载和响应时间

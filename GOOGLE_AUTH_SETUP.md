# Google OAuth Setup Guide

This guide will help you set up Google OAuth authentication for the MedGemma platform.

## Prerequisites

1. A Google Cloud Platform (GCP) account
2. A GCP project with billing enabled
3. Database setup (PostgreSQL recommended)

## Step 1: Create Google OAuth Credentials

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project or create a new one
3. Navigate to "APIs & Services" > "Credentials"
4. Click "Create Credentials" > "OAuth 2.0 Client IDs"
5. Configure the OAuth consent screen if prompted:
   - Choose "External" for user type
   - Fill in the required fields (App name, User support email, etc.)
   - Add your domain to authorized domains
6. For Application type, select "Web application"
7. Add authorized redirect URIs:
   - For development: `http://localhost:3000/api/auth/callback/google`
   - For production: `https://yourdomain.com/api/auth/callback/google`
8. Save and copy the Client ID and Client Secret

## Step 2: Environment Variables

Create a `.env.local` file in your project root and add:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/medgemma"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"  # Change to your production URL
NEXTAUTH_SECRET="your-random-secret-key-here"  # Generate a random string

# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

## Step 3: Database Setup

1. Install and run PostgreSQL
2. Create a database named `medgemma`
3. Run Prisma migrations:

```bash
npx prisma generate
npx prisma db push
```

## Step 4: Generate NextAuth Secret

Generate a secure random string for NEXTAUTH_SECRET:

```bash
openssl rand -base64 32
```

## Step 5: Test the Setup

1. Start the development server:
```bash
npm run dev
```

2. Navigate to your application
3. Click the "Sign in with Google" button
4. Complete the OAuth flow

## Production Deployment

For production deployment:

1. Update `NEXTAUTH_URL` to your production domain
2. Add your production domain to Google OAuth authorized redirect URIs
3. Ensure your database is accessible from your production environment
4. Set all environment variables in your hosting platform

## Troubleshooting

### Common Issues:

1. **"redirect_uri_mismatch" error**: 
   - Check that your redirect URI in Google Console matches exactly
   - Ensure no trailing slashes

2. **Database connection errors**:
   - Verify DATABASE_URL is correct
   - Ensure database is running and accessible

3. **NextAuth errors**:
   - Check that NEXTAUTH_SECRET is set
   - Verify NEXTAUTH_URL matches your domain

### Security Notes:

- Never commit `.env.local` to version control
- Use strong, unique secrets for production
- Regularly rotate OAuth credentials
- Monitor OAuth usage in Google Console

## Additional Resources

- [NextAuth.js Documentation](https://next-auth.js.org/)
- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Prisma Documentation](https://www.prisma.io/docs/)

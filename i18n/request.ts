import { getRequestConfig } from 'next-intl/server';
import { routing } from './routing';
import { localePaths } from '../locales';

export default getRequestConfig(async ({ requestLocale }) => {
    // This typically corresponds to the `[locale]` segment
    let locale = await requestLocale;

    // Ensure that the incoming `locale` is valid
    if (!locale || !routing.locales.includes(locale as any)) {
        locale = routing.defaultLocale;
    }

    // Load and merge message files for this locale
    const loadMessages = async (locale: string) => {
        // Common translations
        const common = (await import(`../locales/${localePaths.common}/${locale}.json`)).default;

        // Page-specific translations using path configurations from locales/index.ts
        const home = (await import(`../locales/${localePaths.pages.home}/${locale}.json`)).default;
        const notFound = (await import(`../locales/${localePaths.pages.notFound}/${locale}.json`)).default;
        const privacy = (await import(`../locales/${localePaths.pages.privacy}/${locale}.json`)).default;
        const terms = (await import(`../locales/${localePaths.pages.terms}/${locale}.json`)).default;
        const medgemma = (await import(`../locales/pages/medgemma/${locale}.json`)).default;
        const medicalChat = (await import(`../locales/${localePaths.pages.medicalChat}/${locale}.json`)).default;
        const pricing = (await import(`../locales/${localePaths.pages.pricing}/${locale}.json`)).default;

        // Merge all translations
        return {
            ...common,
            pages: {
                home,
                notFound,
                privacy,
                terms,
                medgemma,
                medicalChat,
                pricing
            }
        };
    };

    // Return the configuration
    return {
        locale,
        messages: await loadMessages(locale)
    };
});
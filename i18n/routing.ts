import { createNavigation } from 'next-intl/navigation';
import { defineRouting } from 'next-intl/routing';
import { supportedLocales, defaultLocale as configDefaultLocale, localeNames } from '../locales';

export const locales = supportedLocales;
export const defaultLocale = configDefaultLocale;

export const routing = defineRouting({
    locales,
    defaultLocale,
    localeDetection: false,
    localePrefix: 'as-needed',
    pathnames: {
        '/': '/',
        '/privacy-policy': '/privacy-policy',
        '/terms-of-service': '/terms-of-service',
        '/medgemma': '/medgemma',
        '/medical-chat': '/medical-chat',
        '/tracking-demo': '/tracking-demo',
        // 404 page is handled automatically
    }
});

export type Pathnames = keyof typeof routing.pathnames;

export const { Link, redirect, usePathname, useRouter } = createNavigation(routing);

export type Locale = (typeof locales)[number];

export const fullLanguageNames = localeNames;
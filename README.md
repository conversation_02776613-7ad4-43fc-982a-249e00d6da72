# TMPL Lite

A lightweight template application built with Next.js, Prisma, and PostgreSQL.

## Project Structure

```
/
├── app/                    # Next.js app directory
│   └── [locale]/           # Internationalized routes
│       ├── privacy-policy/ # Privacy policy page
│       └── terms-of-service/ # Terms of service page
├── components/             # React components
│   └── ui/                 # UI components
├── hooks/                  # Custom React hooks
├── i18n/                   # Internationalization configuration
├── locales/                # Translation files
├── prisma/                 # Prisma schema and migrations
│   ├── schema.prisma
│   └── migrations/
├── public/                 # Static files
│   └── social-icons/       # Social media icons
├── utils/                  # Utility functions
├── .env                    # 本地开发环境变量
├── .env.production         # 生产环境变量
├── Dockerfile              # Docker 构建配置
├── docker-compose.yml      # 本地开发 Docker 配置
├── docker-compose.prod.yml # 生产环境 Docker 配置
├── docker-compose.prod.local.yml # 本地测试生产环境配置
├── init.sh                 # 项目初始化脚本
├── build.sh                # 构建生产镜像脚本
├── deploy.sh               # 部署生产环境脚本
└── test-prod.sh            # 本地测试生产环境脚本
```

## 环境变量配置

### 配置文件
- `.env`: 本地开发环境配置，包含应用信息、数据库连接、安全密钥等
- `.env.production`: 生产环境配置，包含生产 URL、数据库连接、安全密钥等（不提交到 Git）

### 主要配置项

#### 应用程序配置
```
# 环境标识
NODE_ENV=development|production

# 应用程序信息
NEXT_PUBLIC_APP_NAME="TMPL Lite"
NEXT_PUBLIC_APP_DESCRIPTION="A lightweight template application"
NEXT_PUBLIC_PROJECT_IDENTIFIER=medgemma

# API 和站点 URL
NEXT_PUBLIC_API_URL=http://localhost:3000|https://api.tmpl-lite.com
NEXT_PUBLIC_SITE_URL=http://localhost:3000|https://domain.com
NEXTAUTH_URL=http://localhost:3000|https://domain.com
```

#### 数据库配置
```
# 数据库凭证
POSTGRES_USER=medgemma_admin
POSTGRES_PASSWORD=medgemma_2024|medgemma_2024_prod
POSTGRES_DB=medgemma

# 数据库连接 URL
DATABASE_URL="postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@localhost:5432/${POSTGRES_DB}?schema=public"
```

#### 安全配置
```
# 安全密钥
NEXTAUTH_SECRET=medgemma_dev_secret|generate_a_strong_secret_key_here
API_SECRET_KEY=dev_api_secret_key|generate_a_strong_api_key_here
```

#### 第三方服务配置
```
# Google OAuth
NEXT_PUBLIC_GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# Google Cloud
GOOGLE_CLOUD_PROJECT=
GOOGLE_CLOUD_LOCATION=
GOOGLE_APPLICATION_CREDENTIALS=./google_application_credentials.json|/app/google_application_credentials.json

# 分析和监控
NEXT_PUBLIC_GA_ID=
NEXT_PUBLIC_FEISHU_NOTIFY_WEBHOOK_URL=
NEXT_PUBLIC_FEISHU_KEY_WEBHOOK_URL=
```

### 设置步骤
1. 本地开发环境设置：
```bash
# 复制并编辑 .env 文件
cp .env.example .env  # 如果有示例文件
# 或手动创建
touch .env
```

2. 使用方式：
- 本地开发：使用 `.env` 文件（NODE_ENV=development）
- 生产环境：使用 `.env.production` 文件（NODE_ENV=production）

## 开始使用

1. 安装依赖：
```bash
npm install
```

2. 启动开发环境（使用 Docker）：
```bash
# 启动数据库容器
docker-compose up -d medgemma_db

# 启动开发服务器
npm run dev
```

3. 或者完整启动开发环境：
```bash
docker-compose up -d
```

4. 运行数据库迁移：
```bash
npx prisma migrate dev
```

5. 生成 Prisma 客户端：
```bash
npx prisma generate
```

## 数据库模型

### User
- 用户基本信息（ID、邮箱、名称、头像）
- 创建和更新时间
- 与 Todo 的关联

### Todo
- 任务管理（标题、描述、完成状态）
- 创建和更新时间
- 与用户的关联

## 项目初始化

如果您要基于此模板创建新项目，可以使用初始化脚本：

```bash
./init.sh
```

此脚本将引导您：
1. 输入新项目名称
2. 配置生产环境域名
3. 设置飞书 Webhook
4. 配置 Docker 和数据库信息

脚本会自动替换项目中的相关信息，包括环境变量、Docker 配置等。

## 本地测试生产环境

1. 使用测试脚本启动本地生产环境：
```bash
./test-prod.sh
```

2. 或者手动启动：
```bash
docker-compose -f docker-compose.prod.local.yml up -d
```

3. 访问本地生产环境：
- 应用：http://localhost:3001
- 数据库：localhost:5433

## 部署到生产环境

1. 构建生产镜像：
```bash
# 使用构建脚本
./build.sh

# 或手动构建
docker buildx build --platform linux/amd64 --build-arg NODE_ENV=production -t your-username/your-project:latest --push .
```

2. 配置生产环境变量：
- 确保 `.env.production` 包含正确的生产配置
- 替换占位符为实际的生产值

3. 部署到生产服务器：
```bash
# 在生产服务器上
./deploy.sh
```

4. 部署脚本会：
- 拉取最新的 Docker 镜像
- 停止现有容器
- 启动新容器
- 运行数据库迁移

## 开发指南

1. 环境变量：
   - 不要提交敏感信息到 Git
   - 使用 `.env` 进行本地开发
   - 使用 `.env.production` 进行生产部署（使用占位符）

2. 数据库：
   - 始终为架构更改创建迁移
   - 在部署前在本地测试迁移
   - 定期备份生产数据库

3. Docker：
   - 使用 `docker-compose.yml` 进行开发
   - 使用 `docker-compose.prod.local.yml` 进行本地生产测试
   - 使用 `docker-compose.prod.yml` 进行生产部署
   - 确保生产配置安全

# MedGemma Hub

A comprehensive platform showcasing MedGemma AI models by Google DeepMind for medical text and image analysis.

## Features

- **Interactive Demo**: Test MedGemma models with real-time responses
- **Multilingual Support**: Available in English and Chinese
- **Responsive Design**: Optimized for desktop and mobile devices
- **Modern UI**: Built with Next.js, React, and TailwindCSS
- **Med Gemini Integration**: Test page for Vertex AI Med Gemini models

## Med Gemini API Setup

This project includes a test page for Google Vertex AI's Med Gemini models (different from MedGemma). Med Gemini uses the standard Gemini models optimized for medical applications.

### Prerequisites

1. **Google Cloud Project**: You need a Google Cloud project with Vertex AI API enabled
2. **Authentication**: Set up Google Cloud authentication
3. **Billing**: Ensure billing is enabled for your Google Cloud project

### Authentication Setup

Choose one of the following methods:

#### Method 1: Service Account Key File (Development)

1. Create a service account in Google Cloud Console
2. Download the JSON key file
3. Rename it to `google_application_credentials.json`
4. Place it in the project root directory

#### Method 2: Environment Variables (Recommended for Production)

Set the following environment variables:

```bash
export GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/credentials.json
export GOOGLE_CLOUD_PROJECT=your-project-id
export GOOGLE_CLOUD_LOCATION=us-central1
```

### Available Models

The Med Gemini API supports the following models:
- `gemini-1.5-flash` (Default - Balanced performance and cost)
- `gemini-1.5-pro` (High performance for complex medical reasoning)
- `gemini-2.0-flash` (Latest with enhanced features)

### API Endpoints

- `POST /api/medgemma` - Send medical queries to Med Gemini
- `GET /api/medgemma?health=true` - Health check

### Testing the Integration

1. Navigate to `/medgemma-test` in your browser
2. Check the API health status
3. Try the example medical questions
4. Submit your own medical queries

### Important Notes

- **Research Use Only**: Med Gemini responses are for educational and research purposes
- **Not Medical Advice**: AI responses should not replace professional medical consultation
- **Safety Filters**: The API includes medical safety filters and content moderation
- **Rate Limits**: Be aware of Vertex AI rate limits and quotas

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: TailwindCSS
- **Internationalization**: next-intl
- **AI Integration**: Google Cloud Vertex AI
- **Authentication**: Google Cloud Auth Library

## Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up Google Cloud authentication (see above)
4. Run the development server: `npm run dev`
5. Visit `http://localhost:3000` to see the application
6. Test Med Gemini at `http://localhost:3000/medgemma-test`

## Environment Variables

```bash
# Google Cloud Configuration
GOOGLE_APPLICATION_CREDENTIALS=./google_application_credentials.json|/app/google_application_credentials.json
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1

# Next.js Configuration
NODE_ENV=development|production
```

## Deployment

The application is containerized and can be deployed using Docker:

```bash
# Build the application
npm run build

# Build Docker image
docker build -t medgemma-hub .

# Run container
docker run -p 3000:3000 medgemma-hub
```

## License

This project is for educational and research purposes. Please refer to Google's terms of service for Vertex AI and MedGemma usage guidelines.

## Disclaimer

This application is for research and educational purposes only. It should not be used for actual medical diagnosis or treatment. Always consult qualified healthcare professionals for medical advice.
